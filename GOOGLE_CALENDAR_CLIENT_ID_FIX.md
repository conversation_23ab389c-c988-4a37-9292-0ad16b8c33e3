# Google Calendar Client ID Fix

## 🚨 Problem
Google Calendar OAuth was failing with "Access blocked: Authorization Error - Missing required parameter: client_id" because it was using a different environment variable than Gmail.

## 🔍 Root Cause Analysis

**Environment Variable Inconsistency:**

| Service | Environment Variable Used | Status |
|---------|---------------------------|---------|
| Gmail | `GOOGLE_CLIENT_ID` | ✅ Correct |
| Google Calendar | `NUXT_PUBLIC_GOOGLE_CLIENT_ID` | ❌ Wrong |
| YouTube | `GOOGLE_CLIENT_ID` | ✅ Correct |

The Google Calendar composable was looking for `NUXT_PUBLIC_GOOGLE_CLIENT_ID` which likely wasn't set, causing the client_id parameter to be empty in the OAuth request.

## ✅ Solution Applied

**Fixed Google Calendar to use the same environment variable as other Google services:**

### Before:
```typescript
// layers/auth-module/composables/google-calendar.ts
const GOOGLE_CALENDAR_CONFIG: GoogleCalendarConfig = {
  clientId: process.env.NUXT_PUBLIC_GOOGLE_CLIENT_ID || '',  // ❌ Wrong
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  // ...
}
```

### After:
```typescript
// layers/auth-module/composables/google-calendar.ts
const GOOGLE_CALENDAR_CONFIG: GoogleCalendarConfig = {
  clientId: process.env.GOOGLE_CLIENT_ID || '',  // ✅ Correct
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  // ...
}
```

## 📋 Consistent Google Services Configuration

**All Google services now use the same environment variables:**

```bash
# Single set of Google OAuth credentials for all services
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-console
```

**Services using these credentials:**
- ✅ Gmail (`GOOGLE_CLIENT_ID`)
- ✅ Google Calendar (`GOOGLE_CLIENT_ID`) - Fixed
- ✅ YouTube (`GOOGLE_CLIENT_ID`)

## 🔧 Why This Makes Sense

1. **Single OAuth App**: All Google services should use the same Google Cloud Console OAuth application
2. **Shared Credentials**: Gmail, Calendar, and YouTube are all Google services
3. **Simplified Configuration**: One set of credentials instead of multiple
4. **Consistent Scopes**: Each service requests only the scopes it needs

## 🧪 Testing

After this fix:
1. Ensure your `.env` file has `GOOGLE_CLIENT_ID` set (not `NUXT_PUBLIC_GOOGLE_CLIENT_ID`)
2. Try connecting Google Calendar again
3. The OAuth request should now include the correct client_id parameter
4. Google Calendar connection should complete successfully

## 📝 Environment Variable Setup

Make sure your `.env` file contains:

```bash
# Google Services (Gmail + Google Calendar + YouTube)
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-console

# Base URL for OAuth redirects
NUXT_PUBLIC_BASE_URL=https://yourdomain.com  # Production
# NUXT_PUBLIC_BASE_URL=http://localhost:3000  # Development
```

## 🎯 Result

Google Calendar now uses the same OAuth credentials as Gmail and YouTube, eliminating the "missing client_id" error and ensuring consistent authentication across all Google services.
