# LLM Integration Architecture Guide

## LEVER Framework Compliance

### L - Leverage
- **Firebase Infrastructure**: Built on proven Firebase Auth + Firestore foundation
- **Nuxt 3 Framework**: Leverages server-side API routes and composable patterns
- **Existing Security**: Uses established encryption utilities and patterns
- **Design System**: Extends Tairo UI components for consistent user experience

### E - Extend
- **Multi-Provider Support**: Extended single-provider patterns to support 12+ LLM providers
- **Advanced Validation**: Enhanced basic validation with provider-specific testing
- **Inheritance System**: Extended simple user integrations to multi-level inheritance
- **Error Classification**: Enhanced generic error handling with provider-specific patterns

### V - Verify
- **Real-time Validation**: API key testing against live provider endpoints
- **Security Audits**: Regular verification of encryption and access patterns
- **Integration Testing**: Comprehensive testing across all supported providers
- **Performance Monitoring**: Continuous verification of system performance

### E - Eliminate
- **Provider Coupling**: Eliminated tight coupling between UI and specific providers
- **Duplicate Code**: Removed redundant validation and error handling patterns
- **Security Vulnerabilities**: Eliminated plaintext credential storage
- **Configuration Complexity**: Removed provider-specific configuration duplication

### R - Reduce
- **Setup Complexity**: Simplified integration creation to essential steps
- **Cognitive Load**: Reduced complexity through consistent UI patterns
- **Maintenance Burden**: Automated configuration generation and validation
- **Development Overhead**: Centralized provider definitions and patterns

## System Architecture Overview

### Purpose
The LLM Integration Architecture provides a unified, secure, and extensible framework for connecting to multiple Large Language Model providers within the PIB application. The system abstracts provider differences while maintaining provider-specific optimizations.

### Core Principles
1. **Provider Agnostic**: Consistent interface across all LLM providers
2. **Security First**: Encrypted credential storage and secure transmission
3. **User-Centric**: Multi-level integration sharing and inheritance
4. **Developer Friendly**: Clear patterns and extensible architecture
5. **Performance Optimized**: Caching, validation, and error resilience

## Architectural Components

### 1. Provider Configuration System

#### Central Configuration (`/layers/auth-module/config/llm-providers.ts`)

```mermaid
graph TB
    A[LLM Provider Config] --> B[Provider Definitions]
    A --> C[Model Metadata]
    A --> D[API Specifications]

    B --> B1[Basic Info]
    B --> B2[Authentication]
    B --> B3[Capabilities]

    C --> C1[Model Lists]
    C --> C2[Pricing Data]
    C --> C3[Context Windows]

    D --> D1[Endpoints]
    D --> D2[Headers]
    D --> D3[Parameters]
```

#### Provider Definition Structure
```typescript
interface LLMProvider {
  id: string
  name: string
  icon: string
  description: string
  website: string
  documentation: string

  // API Configuration
  api: {
    baseUrl: string
    chat: string
    embeddings?: string
    models?: string
    headers: Record<string, string>
  }

  // Authentication
  auth: {
    type: 'api-key' | 'oauth' | 'bearer'
    keyFormat?: RegExp
    description: string
  }

  // Models and capabilities
  models: LLMModel[]
  capabilities: string[]
  features: {
    streaming: boolean
    functionCalling: boolean
    vision: boolean
    multimodal: boolean
  }
}
```

### 2. Data Architecture

#### Integration Data Model
```mermaid
erDiagram
    User ||--o{ Integration : creates
    Workspace ||--o{ Integration : shares
    Profile ||--o{ Integration : inherits

    Integration {
        string id PK
        string userId FK
        string workspaceId FK
        string profileId FK
        LLMProvider provider
        object credentials
        object settings
        boolean isActive
        boolean isDefault
        boolean availableToProfiles
        timestamp createdAt
        timestamp updatedAt
        timestamp lastUsedAt
    }
```

#### Multi-Level Inheritance System
```typescript
interface IntegrationInheritance {
  // User-level: Available across all workspaces
  user: Integration[]

  // Workspace-level: Shared within workspace
  workspace: Integration[]

  // Profile-level: Role-specific overrides
  profile: Integration[]

  // Resolution priority: Profile > Workspace > User
  resolved: Integration[]
}
```

### 3. Security Architecture

#### Encryption System (`/layers/auth-module/utils/encryption.ts`)

```mermaid
sequenceDiagram
    participant UI as Frontend
    participant API as Encrypt API
    participant DB as Firestore

    UI->>API: API Key + Provider
    API->>API: Generate Salt & IV
    API->>API: Derive Key (PBKDF2)
    API->>API: Encrypt (AES-256-GCM)
    API-->>UI: Encrypted Payload
    UI->>DB: Store Encrypted Data
```

#### Security Implementation Details
- **Algorithm**: AES-256-GCM with authenticated encryption
- **Key Derivation**: PBKDF2 with 100,000 iterations
- **Salt Management**: Unique salt per encryption operation
- **Access Control**: User-scoped encryption keys
- **Audit Trail**: Comprehensive logging of security operations

### 4. Validation System

#### Multi-Layer Validation Strategy

```mermaid
graph LR
    A[API Key Input] --> B[Format Validation]
    B --> C[Length Check]
    C --> D[Provider Pattern]
    D --> E[Live API Test]
    E --> F[Error Classification]
    F --> G[User Feedback]
```

#### Provider-Specific Validation
```typescript
const validationPatterns = {
  openai: /^sk-[A-Za-z0-9]{48}$/,
  anthropic: /^sk-ant-api03-[A-Za-z0-9-]{40,}$/,
  google: /^[A-Z0-9]{39}$/i,
  xai: /^xai-[A-Za-z0-9]{40,}$/,
  // ... other providers
}

async function validateApiKey(provider: string, apiKey: string) {
  // Format validation
  const pattern = validationPatterns[provider]
  if (!pattern.test(apiKey)) {
    return { valid: false, error: 'Invalid key format' }
  }

  // Live API validation
  return await testProviderApi(provider, apiKey)
}
```

## Frontend Architecture

### 1. Composable System

#### Core Composables
```mermaid
graph TB
    A[useIntegrations] --> B[CRUD Operations]
    A --> C[Real-time Sync]
    A --> D[Validation]

    E[useLLMProviders] --> F[Provider Data]
    E --> G[Model Management]
    E --> H[Capabilities]

    I[useIntegrationInheritance] --> J[Multi-level Resolution]
    I --> K[Override Detection]
    I --> L[Inheritance Display]
```

#### Integration Management (`useIntegrations`)
```typescript
export function useIntegrations() {
  const integrations = ref<Integration[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Real-time Firestore subscription
  const unsubscribe = onSnapshot(
    query(collection(db, 'integrations'), where('userId', '==', userId)),
    (snapshot) => {
      integrations.value = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    }
  )

  // CRUD operations
  const create = async (data: CreateIntegrationData) => { /* ... */ }
  const update = async (id: string, data: UpdateIntegrationData) => { /* ... */ }
  const remove = async (id: string, options?: DeleteOptions) => { /* ... */ }

  return {
    integrations: readonly(integrations),
    loading: readonly(loading),
    error: readonly(error),
    create,
    update,
    remove
  }
}
```

### 2. Component Architecture

#### Integration Form Component
```vue
<template>
  <TairoIntegrationForm
    :provider="selectedProvider"
    :loading="isCreating"
    @submit="handleSubmit"
    @validate="handleValidation"
  >
    <template #provider-selector>
      <TairoProviderGrid
        :providers="availableProviders"
        @select="selectProvider"
      />
    </template>

    <template #credentials>
      <BaseInput
        v-model="apiKey"
        type="password"
        :validation="validation"
        @blur="validateApiKey"
      />
    </template>

    <template #advanced-settings>
      <TairoCollapse title="Advanced Settings">
        <TairoModelSelector
          v-model="selectedModel"
          :provider="selectedProvider"
        />
        <TairoParameterSettings
          v-model="parameters"
        />
      </TairoCollapse>
    </template>
  </TairoIntegrationForm>
</template>
```

#### Integration Card Component
```vue
<template>
  <TairoIntegrationCard
    :integration="integration"
    :inherited="isInherited"
    @toggle="toggleStatus"
    @edit="openEditModal"
    @delete="confirmDelete"
  >
    <template #status-indicator>
      <TairoBadge
        :color="integration.isActive ? 'success' : 'muted'"
        :label="statusLabel"
      />
    </template>

    <template #actions>
      <TairoDropdown>
        <TairoDropdownItem @click="setAsDefault">
          Set as Default
        </TairoDropdownItem>
        <TairoDropdownItem @click="viewUsage">
          View Usage
        </TairoDropdownItem>
        <TairoDropdownItem @click="testConnection">
          Test Connection
        </TairoDropdownItem>
      </TairoDropdown>
    </template>
  </TairoIntegrationCard>
</template>
```

## Backend Architecture

### 1. API Endpoint Structure

```mermaid
graph TB
    A[/api/integrations] --> B[/encrypt]
    A --> C[/validate]
    A --> D[/models]
    A --> E[/{provider}]

    E --> F[/validate]
    E --> G[/oauth]
    E --> H[/models]

    G --> I[/init]
    G --> J[/callback]
    G --> K[/status]
```

#### Core Endpoints

**Encryption Service** (`/api/integrations/encrypt`)
```typescript
export default defineEventHandler(async (event) => {
  const { apiKey, provider } = await readBody(event)

  // Validate user and provider
  const user = await requireAuth(event)
  const providerConfig = getProviderConfig(provider)

  // Encrypt API key
  const encrypted = await encryptCredentials(apiKey, user.id)

  return {
    encryptedKey: encrypted.data,
    encryptedAt: encrypted.timestamp,
    salt: encrypted.salt
  }
})
```

**Validation Service** (`/api/integrations/validate`)
```typescript
export default defineEventHandler(async (event) => {
  const { provider, apiKey } = await readBody(event)

  // Provider-specific validation
  const validator = getProviderValidator(provider)
  const result = await validator.validate(apiKey)

  return {
    valid: result.valid,
    error: result.error,
    metadata: result.metadata
  }
})
```

### 2. Provider-Specific Services

#### Provider Validation Factory
```typescript
class ProviderValidatorFactory {
  static create(provider: string): ProviderValidator {
    switch (provider) {
      case 'openai':
        return new OpenAIValidator()
      case 'anthropic':
        return new AnthropicValidator()
      case 'google':
        return new GoogleValidator()
      default:
        return new GenericValidator()
    }
  }
}

abstract class ProviderValidator {
  abstract async validate(apiKey: string): Promise<ValidationResult>
  abstract getEndpoint(): string
  abstract getHeaders(apiKey: string): Record<string, string>
  abstract parseResponse(response: any): ValidationResult
}
```

## Error Handling System

### 1. Error Classification Framework

```mermaid
graph TB
    A[Error Occurs] --> B{Error Type}
    B --> C[Validation Error]
    B --> D[Network Error]
    B --> E[Provider Error]
    B --> F[System Error]

    C --> G[Format Issues]
    C --> H[Invalid Key]

    D --> I[Connection Timeout]
    D --> J[DNS Resolution]

    E --> K[Rate Limiting]
    E --> L[API Changes]

    F --> M[Encryption Failure]
    F --> N[Database Error]
```

#### Error Handler Implementation
```typescript
class IntegrationErrorHandler {
  static classify(error: any): ClassifiedError {
    if (error.response?.status === 401) {
      return {
        type: 'authentication',
        severity: 'high',
        userMessage: 'Invalid API key. Please check your credentials.',
        action: 'retry_with_new_key'
      }
    }

    if (error.response?.status === 429) {
      return {
        type: 'rate_limit',
        severity: 'medium',
        userMessage: 'Rate limit exceeded. Please try again later.',
        action: 'retry_later'
      }
    }

    // ... other classifications
  }

  static handle(error: ClassifiedError): ErrorResponse {
    // Log error
    this.logError(error)

    // Generate user-friendly response
    return {
      message: error.userMessage,
      action: error.action,
      retryAfter: error.retryAfter
    }
  }
}
```

### 2. Recovery Strategies

#### Automatic Retry Logic
```typescript
class RetryManager {
  static async withRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const { maxAttempts = 3, backoff = 'exponential' } = options

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation()
      }
      catch (error) {
        if (attempt === maxAttempts || !this.isRetryable(error)) {
          throw error
        }

        const delay = this.calculateDelay(attempt, backoff)
        await this.sleep(delay)
      }
    }
  }

  private static isRetryable(error: any): boolean {
    return [408, 429, 500, 502, 503, 504].includes(error.response?.status)
  }
}
```

## Performance Optimization

### 1. Caching Strategy

```mermaid
graph LR
    A[Request] --> B{Cache Hit?}
    B -->|Yes| C[Return Cached]
    B -->|No| D[Fetch Data]
    D --> E[Update Cache]
    E --> F[Return Data]
```

#### Multi-Level Caching
```typescript
class CacheManager {
  private memoryCache = new Map()
  private persistentCache = new PersistentCache()

  async get<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    // Check memory cache
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key)
    }

    // Check persistent cache
    const cached = await this.persistentCache.get(key)
    if (cached && !this.isExpired(cached)) {
      this.memoryCache.set(key, cached.data)
      return cached.data
    }

    // Fetch fresh data
    const data = await fetcher()
    this.memoryCache.set(key, data)
    await this.persistentCache.set(key, data)

    return data
  }
}
```

### 2. Lazy Loading and Code Splitting

#### Dynamic Provider Loading
```typescript
async function loadProvider(providerId: string) {
  const module = await import(`../providers/${providerId}.js`)
  return module.default
}

// Component-level code splitting
const TairoIntegrationForm = defineAsyncComponent(() =>
  import('~/components/tairo/TairoIntegrationForm.vue')
)
```

## Testing Strategy

### 1. Test Pyramid Structure

```mermaid
pyramid
    title Integration Testing Pyramid

    level1: E2E Tests
    level2: Integration Tests
    level3: Unit Tests
    level4: Static Analysis
```

#### Test Categories

**Unit Tests** - Component and function testing
```typescript
describe('validateApiKey', () => {
  it('should validate OpenAI key format', () => {
    const result = validateApiKey('openai', `sk-${'x'.repeat(48)}`)
    expect(result.formatValid).toBe(true)
  })

  it('should reject invalid key format', () => {
    const result = validateApiKey('openai', 'invalid-key')
    expect(result.formatValid).toBe(false)
  })
})
```

**Integration Tests** - API endpoint testing
```typescript
describe('/api/integrations/validate', () => {
  it('should validate API key against provider', async () => {
    const response = await $fetch('/api/integrations/validate', {
      method: 'POST',
      body: { provider: 'openai', apiKey: 'valid-key' }
    })

    expect(response.valid).toBe(true)
  })
})
```

**E2E Tests** - Complete user workflows
```typescript
test('create integration workflow', async ({ page }) => {
  await page.goto('/user/integrations')
  await page.click('[data-testid="add-integration"]')
  await page.selectOption('[data-testid="provider"]', 'openai')
  await page.fill('[data-testid="api-key"]', 'sk-test-key')
  await page.click('[data-testid="save"]')

  await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
})
```

### 2. Security Testing

#### Security Test Suite
```typescript
describe('Security Tests', () => {
  it('should encrypt API keys before storage', async () => {
    const response = await createIntegration({
      provider: 'openai',
      apiKey: 'sk-plaintext-key'
    })

    // Verify key is encrypted in database
    const stored = await getStoredIntegration(response.id)
    expect(stored.credentials.apiKey).not.toContain('sk-plaintext-key')
    expect(stored.credentials.apiKey).toMatch(/^[A-Z0-9+/]+=*$/i) // Base64
  })

  it('should prevent unauthorized access', async () => {
    const response = await fetch('/api/integrations', {
      headers: { Authorization: 'invalid-token' }
    })

    expect(response.status).toBe(401)
  })
})
```

## Monitoring and Observability

### 1. Metrics Collection

#### Key Performance Indicators
```typescript
const metrics = {
  // Success rates
  integrationCreateSuccess: counter(),
  validationSuccess: counter(),
  authenticationSuccess: counter(),

  // Performance metrics
  validationDuration: histogram(),
  encryptionDuration: histogram(),
  apiResponseTime: histogram(),

  // Error rates
  validationErrors: counter(['provider', 'error_type']),
  connectionErrors: counter(['provider', 'status_code']),
  systemErrors: counter(['component', 'error_class'])
}
```

#### Custom Monitoring Hooks
```typescript
export function useIntegrationMetrics() {
  const trackValidation = (provider: string, success: boolean, duration: number) => {
    metrics.validationDuration.observe(duration)

    if (success) {
      metrics.validationSuccess.inc({ provider })
    }
    else {
      metrics.validationErrors.inc({ provider })
    }
  }

  const trackIntegrationCreate = (provider: string, success: boolean) => {
    if (success) {
      metrics.integrationCreateSuccess.inc({ provider })
    }
  }

  return { trackValidation, trackIntegrationCreate }
}
```

### 2. Alerting System

#### Alert Definitions
```yaml
alerts:
  - name: HighValidationFailureRate
    condition: rate(validation_errors_total[5m]) > 0.1
    description: 'Validation failure rate is above 10%'
    severity: warning

  - name: ProviderApiDown
    condition: rate(connection_errors_total{status_code="502"}[2m]) > 0.5
    description: Provider API appears to be down
    severity: critical

  - name: EncryptionFailures
    condition: rate(system_errors_total{component="encryption"}[1m]) > 0
    description: Encryption system experiencing failures
    severity: critical
```

## Deployment and Operations

### 1. Environment Configuration

#### Configuration Management
```typescript
// Environment-specific configuration
export const config = {
  development: {
    encryption: {
      keyRotationDays: 30,
      algorithm: 'aes-256-gcm'
    },
    validation: {
      timeout: 5000,
      retries: 2
    }
  },
  production: {
    encryption: {
      keyRotationDays: 90,
      algorithm: 'aes-256-gcm'
    },
    validation: {
      timeout: 10000,
      retries: 3
    }
  }
}
```

### 2. Migration and Versioning

#### Database Migration Strategy
```typescript
class IntegrationMigration {
  async migrateV1ToV2() {
    // Migrate from single-level to multi-level inheritance
    const integrations = await this.getAllIntegrations()

    for (const integration of integrations) {
      if (!integration.inheritanceLevel) {
        await this.updateIntegration(integration.id, {
          inheritanceLevel: 'user',
          availableToProfiles: true
        })
      }
    }
  }

  async migrateEncryption() {
    // Migrate to new encryption algorithm
    const integrations = await this.getEncryptedIntegrations()

    for (const integration of integrations) {
      const decrypted = await this.legacyDecrypt(integration.credentials)
      const reencrypted = await this.newEncrypt(decrypted)

      await this.updateCredentials(integration.id, reencrypted)
    }
  }
}
```

## Future Enhancements

### 1. Planned Features

#### Advanced Integration Capabilities
- **Batch Operations**: Bulk integration management
- **Integration Templates**: Pre-configured integration sets
- **Usage Analytics**: Detailed usage tracking and optimization
- **Cost Management**: Provider cost tracking and budgeting
- **A/B Testing**: Provider performance comparison

#### Enhanced Security
- **Hardware Security Modules**: HSM integration for enterprise deployments
- **Zero-Knowledge Architecture**: Client-side encryption with server blindness
- **Audit Compliance**: SOC 2, HIPAA, and GDPR compliance features
- **Advanced Threat Detection**: Anomaly detection and response

### 2. Extensibility Roadmap

#### Plugin Architecture
```typescript
interface IntegrationPlugin {
  readonly name: string
  readonly version: string

  providers: () => ProviderDefinition[]
  validators: () => ValidatorDefinition[]
  enhancers: () => UIEnhancement[]

  install: (context: PluginContext) => Promise<void>
  uninstall: (context: PluginContext) => Promise<void>
}
```

#### Custom Provider Support
- **Plugin System**: Third-party provider plugin support
- **Custom Validators**: User-defined validation logic
- **Webhook Integration**: Real-time integration status updates
- **API Extensions**: Custom endpoint creation and management

## Conclusion

The LLM Integration Architecture provides a comprehensive, secure, and maintainable foundation for managing multiple LLM provider integrations. The system balances flexibility with consistency, enabling rapid addition of new providers while maintaining security and user experience standards.

### Architecture Strengths
- ✅ **Unified Interface**: Consistent patterns across all providers
- ✅ **Security First**: Comprehensive encryption and access control
- ✅ **User-Centric**: Multi-level inheritance and sharing
- ✅ **Developer Friendly**: Clear patterns and extensible design
- ✅ **Performance Optimized**: Caching, validation, and error resilience
- ✅ **Well Tested**: Comprehensive test coverage and security validation

### Success Metrics
- **Provider Support**: 12+ LLM providers with consistent interface
- **Security**: Zero plaintext credential storage, full encryption
- **Performance**: <2 second validation, 99.9% uptime
- **User Experience**: Clear error messages, inheritance visualization
- **Developer Experience**: <1 hour to add new provider support
- **Reliability**: Comprehensive error handling and recovery
