import type { Unsubscribe } from 'firebase/firestore'
import type { CalendarCustomAttribute, CalendarEvent } from '../types'
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  Timestamp,

  updateDoc,
  where,
} from 'firebase/firestore'

interface UseCalendarEventsProps {
  fromDate: MaybeRefOrGetter<Date>
  toDate: MaybeRefOrGetter<Date>
  calendarId?: MaybeRefOrGetter<string | null> // 'all' or specific calendar ID
}

// Utility function to convert Firestore timestamp to Date
function timestampToDate(timestamp: any): Date {
  if (timestamp && timestamp.toDate) {
    return timestamp.toDate()
  }
  if (timestamp instanceof Date) {
    return timestamp
  }
  return new Date(timestamp)
}

// Convert CalendarEvent to Firestore format
function eventToFirestore(event: Partial<CalendarEvent>) {
  return {
    ...event,
    startDate: event.startDate ? Timestamp.fromDate(event.startDate) : null,
    endDate: event.endDate ? Timestamp.fromDate(event.endDate) : null,
    createdAt: event.createdAt ? Timestamp.fromDate(event.createdAt) : serverTimestamp(),
    updatedAt: serverTimestamp(),
  }
}

// Convert Firestore document to CalendarEvent
function firestoreToEvent(doc: any): CalendarEvent {
  const data = doc.data()
  return {
    id: doc.id,
    ...data,
    startDate: timestampToDate(data.startDate),
    endDate: timestampToDate(data.endDate),
    createdAt: timestampToDate(data.createdAt),
    updatedAt: timestampToDate(data.updatedAt),
  }
}

export function useCalendarEvents(props: UseCalendarEventsProps) {
  const { $firestore } = useNuxtApp()
  const { currentProfile } = useAuth()

  // Get selected calendar for filtering (optional)
  const selectedCalendar = toValue(props.calendarId)
  const defaultCalendar = ref(null)
  const selectedCalendarId = ref(selectedCalendar || 'all')

  const calendarEvents = ref<CalendarCustomAttribute<CalendarEvent>[]>([])
  const pendingEvents = ref<Partial<CalendarEvent>[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  let unsubscribe: Unsubscribe | null = null

  // Convert event to calendar attribute format
  function eventToAttribute(event: CalendarEvent): CalendarCustomAttribute<CalendarEvent> {
    return {
      key: event.id,
      customData: event,
      dates: [event.startDate, event.endDate],
    }
  }

  // Setup real-time subscription to calendar events
  function setupSubscription() {
    if (!currentProfile.value?.workspace_id || !currentProfile.value?.userId) {
      return
    }

    try {
      const fromDate = toValue(props.fromDate)
      const toDate = toValue(props.toDate)
      const selectedCalendarId = toValue(props.calendarId)

      // Build base query
      let eventsQuery = query(
        collection($firestore, 'calendar_events'),
        where('workspaceId', '==', currentProfile.value.workspace_id),
        where('startDate', '>=', Timestamp.fromDate(fromDate)),
        where('startDate', '<=', Timestamp.fromDate(toDate)),
        orderBy('startDate'),
      )

      // Add calendar filter if specific calendar is selected
      if (selectedCalendarId && selectedCalendarId !== 'all') {
        eventsQuery = query(
          collection($firestore, 'calendar_events'),
          where('workspaceId', '==', currentProfile.value.workspace_id),
          where('calendarId', '==', selectedCalendarId),
          where('startDate', '>=', Timestamp.fromDate(fromDate)),
          where('startDate', '<=', Timestamp.fromDate(toDate)),
          orderBy('startDate'),
        )
      }

      unsubscribe = onSnapshot(eventsQuery, (snapshot) => {
        const events = snapshot.docs.map(doc => firestoreToEvent(doc))
        calendarEvents.value = events.map(event => eventToAttribute(event))
        error.value = null
      }, (err) => {
        error.value = `Failed to load calendar events: ${err.message}`
        console.error('Calendar events subscription error:', err)
      })
    }
    catch (err) {
      error.value = `Failed to setup calendar subscription: ${err.message}`
      console.error('Calendar subscription setup error:', err)
    }
  }

  // Load pending events (events without scheduled dates)
  async function loadPendingEvents() {
    if (!currentProfile.value?.workspace_id)
      return

    try {
      const pendingQuery = query(
        collection($firestore, 'calendar_events'),
        where('workspaceId', '==', currentProfile.value.workspace_id),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc'),
      )

      const snapshot = await getDocs(pendingQuery)
      pendingEvents.value = snapshot.docs.map(doc => firestoreToEvent(doc))
    }
    catch (err) {
      console.error('Failed to load pending events:', err)
    }
  }

  // Create new event
  async function createEvent(eventData: Partial<CalendarEvent>): Promise<string | null> {
    if (!currentProfile.value?.workspace_id || !currentProfile.value?.userId) {
      throw new Error('No workspace or user context available')
    }

    try {
      // Determine which calendar to use
      let targetCalendarId = eventData.calendarId

      // If no calendar specified, use selected calendar
      if (!targetCalendarId) {
        const currentSelectedCalendar = toValue(props.calendarId)
        if (currentSelectedCalendar && currentSelectedCalendar !== 'all') {
          targetCalendarId = currentSelectedCalendar
        }
      }

      const newEvent = eventToFirestore({
        ...eventData,
        userId: currentProfile.value.userId,
        workspaceId: currentProfile.value.workspace_id,
        calendarId: targetCalendarId,
        status: 'scheduled',
        createdAt: new Date(),
        updatedAt: new Date(),
      })

      const docRef = await addDoc(collection($firestore, 'calendar_events'), newEvent)
      return docRef.id
    }
    catch (err) {
      error.value = `Failed to create event: ${err.message}`
      throw err
    }
  }

  // Update existing event
  async function updateEvent(eventId: string, updates: Partial<CalendarEvent>): Promise<void> {
    try {
      const eventDoc = doc($firestore, 'calendar_events', eventId)
      const updateData = eventToFirestore(updates)
      await updateDoc(eventDoc, updateData)
    }
    catch (err) {
      error.value = `Failed to update event: ${err.message}`
      throw err
    }
  }

  // Delete event
  async function deleteEvent(eventId: string): Promise<void> {
    try {
      const eventDoc = doc($firestore, 'calendar_events', eventId)
      await deleteDoc(eventDoc)
    }
    catch (err) {
      error.value = `Failed to delete event: ${err.message}`
      throw err
    }
  }

  // Refresh data
  async function refresh() {
    isLoading.value = true
    error.value = null

    try {
      await loadPendingEvents()
      setupSubscription()
    }
    catch (err) {
      error.value = `Failed to refresh calendar: ${err.message}`
    }
    finally {
      isLoading.value = false
    }
  }

  // Watch for date range and calendar changes
  watch([() => toValue(props.fromDate), () => toValue(props.toDate), () => toValue(props.calendarId)], () => {
    if (unsubscribe) {
      unsubscribe()
    }
    refresh()
  }, { immediate: true })

  // Watch for profile changes
  watch(() => currentProfile.value, (newProfile) => {
    if (newProfile) {
      refresh()
    }
  }, { immediate: true })

  // Cleanup on unmount
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
  })

  return {
    calendarEvents,
    pendingEvents,
    isLoading,
    error,
    refresh,
    createEvent,
    updateEvent,
    deleteEvent,
  }
}
