import type { Unsubscribe } from 'firebase/firestore'
import type { CalendarSettings } from '../types'
import {
  doc,
  getDoc,
  onSnapshot,
  serverTimestamp,
  setDoc,
  Timestamp,

  updateDoc,
} from 'firebase/firestore'

const defaultSettings: CalendarSettings = {
  hideWeekends: false,
  hourOpen: 8,
  hourClose: 18,
  hourPrecision: 15,
  hourHeight: 160,
  dayOffsetY: 0,
  weekStartsOn: 0,
}

export function useCalendarSettings() {
  const { $firestore } = useNuxtApp()
  const { currentProfile } = useAuth()

  const settings = ref<CalendarSettings>({ ...defaultSettings })
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  let unsubscribe: Unsubscribe | null = null

  // Get settings document ID
  const getSettingsId = () => {
    if (!currentProfile.value?.userId || !currentProfile.value?.workspace_id) {
      return null
    }
    return `${currentProfile.value.userId}_${currentProfile.value.workspace_id}`
  }

  // Convert settings to Firestore format
  function settingsToFirestore(settings: CalendarSettings) {
    return {
      ...settings,
      createdAt: settings.createdAt ? Timestamp.fromDate(settings.createdAt) : serverTimestamp(),
      updatedAt: serverTimestamp(),
    }
  }

  // Convert Firestore document to settings
  function firestoreToSettings(doc: any): CalendarSettings {
    const data = doc.data()
    return {
      ...data,
      id: doc.id,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    }
  }

  // Load settings from Firestore
  async function loadSettings() {
    const settingsId = getSettingsId()
    if (!settingsId)
      return

    isLoading.value = true
    error.value = null

    try {
      const settingsDoc = doc($firestore, 'calendar_settings', settingsId)
      const snapshot = await getDoc(settingsDoc)

      if (snapshot.exists()) {
        settings.value = firestoreToSettings(snapshot)
      }
      else {
        // Create default settings if none exist
        await saveSettings(defaultSettings)
      }
    }
    catch (err) {
      error.value = `Failed to load calendar settings: ${err.message}`
      console.error('Calendar settings load error:', err)
    }
    finally {
      isLoading.value = false
    }
  }

  // Save settings to Firestore
  async function saveSettings(newSettings: Partial<CalendarSettings>) {
    const settingsId = getSettingsId()
    if (!settingsId || !currentProfile.value?.userId || !currentProfile.value?.workspace_id) {
      throw new Error('No user or workspace context available')
    }

    try {
      const settingsDoc = doc($firestore, 'calendar_settings', settingsId)
      const settingsData = settingsToFirestore({
        ...settings.value,
        ...newSettings,
        userId: currentProfile.value.userId,
        workspaceId: currentProfile.value.workspace_id,
      })

      await setDoc(settingsDoc, settingsData, { merge: true })
    }
    catch (err) {
      error.value = `Failed to save calendar settings: ${err.message}`
      throw err
    }
  }

  // Update specific settings
  async function updateSettings(updates: Partial<CalendarSettings>) {
    const settingsId = getSettingsId()
    if (!settingsId)
      return

    try {
      const settingsDoc = doc($firestore, 'calendar_settings', settingsId)
      const updateData = settingsToFirestore(updates)
      await updateDoc(settingsDoc, updateData)
    }
    catch (err) {
      error.value = `Failed to update calendar settings: ${err.message}`
      throw err
    }
  }

  // Setup real-time subscription to settings
  function setupSubscription() {
    const settingsId = getSettingsId()
    if (!settingsId)
      return

    try {
      const settingsDoc = doc($firestore, 'calendar_settings', settingsId)

      unsubscribe = onSnapshot(settingsDoc, (snapshot) => {
        if (snapshot.exists()) {
          settings.value = firestoreToSettings(snapshot)
        }
        error.value = null
      }, (err) => {
        error.value = `Failed to sync calendar settings: ${err.message}`
        console.error('Calendar settings subscription error:', err)
      })
    }
    catch (err) {
      error.value = `Failed to setup settings subscription: ${err.message}`
      console.error('Calendar settings subscription setup error:', err)
    }
  }

  // Reset settings to defaults
  async function resetSettings() {
    await saveSettings(defaultSettings)
  }

  // Watch for profile changes
  watch(() => currentProfile.value, async (newProfile) => {
    if (unsubscribe) {
      unsubscribe()
    }

    if (newProfile) {
      await loadSettings()
      setupSubscription()
    }
  }, { immediate: true })

  // Cleanup on unmount
  onUnmounted(() => {
    if (unsubscribe) {
      unsubscribe()
    }
  })

  return {
    settings,
    isLoading,
    error,
    loadSettings,
    saveSettings,
    updateSettings,
    resetSettings,
  }
}
