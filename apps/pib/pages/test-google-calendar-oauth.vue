<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Google Calendar OAuth Test</h1>
    
    <div class="space-y-4">
      <div>
        <h2 class="text-lg font-semibold">Test Status</h2>
        <p>Connecting: {{ isConnecting }}</p>
        <p>Error: {{ error }}</p>
      </div>
      
      <button 
        @click="testOAuth" 
        :disabled="isConnecting"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {{ isConnecting ? 'Connecting...' : 'Test Google Calendar OAuth' }}
      </button>
      
      <div v-if="messages.length > 0" class="mt-4">
        <h3 class="text-lg font-semibold">Messages:</h3>
        <ul class="list-disc list-inside space-y-1">
          <li v-for="(message, index) in messages" :key="index" class="text-sm">
            {{ message }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
const isConnecting = ref(false)
const error = ref(null)
const messages = ref([])

function addMessage(message) {
  messages.value.push(`${new Date().toLocaleTimeString()}: ${message}`)
}

async function testOAuth() {
  isConnecting.value = true
  error.value = null
  messages.value = []
  
  try {
    addMessage('Starting OAuth test...')
    
    // Get runtime config
    const config = useRuntimeConfig()
    addMessage(`Base URL: ${config.public.oauth?.google?.baseUrl || 'undefined'}`)
    addMessage(`Client ID: ${config.public.oauth?.google?.clientId || 'undefined'}`)
    
    // Create OAuth URL manually
    const params = new URLSearchParams({
      client_id: config.public.oauth?.google?.clientId || '',
      redirect_uri: `${config.public.oauth?.google?.baseUrl || 'http://localhost:3002'}/api/integrations/google-calendar/callback`,
      scope: [
        'https://www.googleapis.com/auth/calendar',
        'https://www.googleapis.com/auth/calendar.events',
      ].join(' '),
      response_type: 'code',
      access_type: 'offline',
      prompt: 'consent',
      state: JSON.stringify({
        userId: 'test-user-id',
        workspaceId: 'test-workspace-id',
        returnUrl: window.location.href,
      }),
    })

    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params}`
    addMessage(`OAuth URL: ${authUrl}`)

    // Open OAuth popup
    addMessage('Opening OAuth popup...')
    const popup = window.open(
      authUrl,
      'google-calendar-auth',
      'width=500,height=600,scrollbars=yes,resizable=yes',
    )

    if (!popup) {
      throw new Error('Failed to open popup - popup blocker may be active')
    }

    addMessage('Popup opened successfully')

    // Wait for popup to complete OAuth flow
    return new Promise((resolve, reject) => {
      // Listen for OAuth completion via postMessage
      const messageHandler = async (event) => {
        addMessage(`Received message: ${JSON.stringify(event.data)}`)
        
        if (event.data?.type === 'oauth-success' && event.data?.provider === 'google-calendar') {
          window.removeEventListener('message', messageHandler)
          clearInterval(checkClosed)
          isConnecting.value = false

          addMessage('✅ OAuth success received!')
          addMessage('✅ Popup should close automatically')
          resolve()
        }
        else if (event.data?.type === 'oauth-error' && event.data?.provider === 'google-calendar') {
          window.removeEventListener('message', messageHandler)
          clearInterval(checkClosed)
          isConnecting.value = false
          
          const errorMessage = event.data.error || 'Google Calendar connection failed'
          addMessage(`❌ OAuth error: ${errorMessage}`)
          reject(new Error(errorMessage))
        }
      }

      window.addEventListener('message', messageHandler)
      addMessage('Listening for postMessage events...')

      // Also check if window was closed manually
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed)
          window.removeEventListener('message', messageHandler)
          isConnecting.value = false
          addMessage('❌ Popup was closed manually')
          reject(new Error('Google Calendar connection was cancelled'))
        }
      }, 1000)

      // Timeout after 5 minutes
      setTimeout(() => {
        clearInterval(checkClosed)
        window.removeEventListener('message', messageHandler)
        if (popup && !popup.closed) {
          popup.close()
        }
        isConnecting.value = false
        addMessage('❌ OAuth timed out')
        reject(new Error('Google Calendar connection timed out'))
      }, 300000)
    })
  }
  catch (err) {
    isConnecting.value = false
    error.value = err.message
    addMessage(`❌ Error: ${err.message}`)
    throw err
  }
}
</script>
