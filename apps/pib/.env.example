# Firebase Configuration
# Get these values from your Firebase project settings
FIREBASE_API_KEY="AIzaSyDRvCjPSkIZ94nHC-1JnzeT8i_e-lDxgoU"
FIREBASE_AUTH_DOMAIN="partners-in-biz-85059.firebaseapp.com"
FIREBASE_PROJECT_ID="partners-in-biz-85059"
FIREBASE_STORAGE_BUCKET="partners-in-biz-85059.firebasestorage.app"
FIREBASE_MESSAGING_SENDER_ID="430887310034"
FIREBASE_APP_ID="1:430887310034:web:1307b4000ec75dbe47d30b"
FIREBASE_MEASUREMENT_ID="G-D0TZTS7PWS"

# Development Settings
# Set to true to use Firebase emulators in development
FIREBASE_USE_EMULATOR=false

# OAuth Provider Settings (optional)
# These are configured in Firebase Console but may be needed for advanced use cases
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret
# TWITTER_API_KEY=your-twitter-api-key
# TWITTER_API_SECRET=your-twitter-api-secret
# LINKEDIN_CLIENT_ID=your-linkedin-client-id
# LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

NUXT_ENCRYPTION_KEY=4b0ece6768f2cfb55246ce09bb70a443b7c94aa8d4c070658d6a745b4e276dd5

# Google (Gmail + Calendar)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Microsoft (Outlook + Calendar)  
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret

# Social Media (Facebook, Instagram, LinkedIn, Twitter, etc.)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
# ... (see full list in setup guide)