# Email API Documentation

## Overview

The PIB Email API provides secure, RESTful endpoints for managing email accounts, synchronizing messages, and sending emails across multiple providers. All endpoints follow OAuth 2.0 and credential encryption standards.

## Base URL

```
/api/integrations/email/
```

## Authentication

All endpoints require Firebase Authentication. Include the Firebase ID token in the Authorization header:

```http
Authorization: Bearer <firebase_id_token>
```

## Error Handling

All endpoints return standardized error responses:

```typescript
interface ApiError {
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
  path: string
}
```

Common error codes:
- `INVALID_CREDENTIALS` - Authentication failed
- `PROVIDER_ERROR` - Email provider connection failed
- `VALIDATION_ERROR` - Request validation failed
- `RATE_LIMIT_EXCEEDED` - API rate limit exceeded
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions

## Endpoints

### POST /validate

Validates email provider credentials and tests connection.

#### Request

```typescript
interface ValidateEmailRequest {
  provider: 'gmail' | 'outlook' | 'yahoo' | 'imap' | 'exchange' | 'custom'
  credentials: {
    // OAuth providers
    accessToken?: string
    refreshToken?: string

    // Password providers
    email?: string
    password?: string
    appPassword?: string

    // Server configuration (IMAP/SMTP)
    host?: string
    port?: number
    secure?: boolean
    imapHost?: string
    imapPort?: number
    smtpHost?: string
    smtpPort?: number
  }
  testConnection?: boolean // Default: true
}
```

#### Response

```typescript
interface ValidateEmailResponse {
  valid: boolean
  error?: string
  serverCapabilities?: string[]
  supportedFolders?: string[]
  quotaInfo?: {
    used: number
    total: number
    percentage: number
  }
  connectionDetails?: {
    serverType: string
    encryptionType: string
    authMethod: string
  }
}
```

#### Examples

**Gmail OAuth Validation:**
```http
POST /api/integrations/email/validate
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "provider": "gmail",
  "credentials": {
    "accessToken": "ya29.a0AfH6SMC...",
    "refreshToken": "1//04-rZ9..."
  },
  "testConnection": true
}
```

**Response:**
```json
{
  "valid": true,
  "serverCapabilities": ["IMAP4", "CONDSTORE", "QRESYNC"],
  "supportedFolders": ["INBOX", "SENT", "DRAFTS", "TRASH", "SPAM"],
  "quotaInfo": {
    "used": **********,
    "total": ***********,
    "percentage": 12.7
  }
}
```

**IMAP Server Validation:**
```http
POST /api/integrations/email/validate
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "provider": "imap",
  "credentials": {
    "email": "<EMAIL>",
    "password": "encrypted_password",
    "imapHost": "imap.example.com",
    "imapPort": 993,
    "smtpHost": "smtp.example.com",
    "smtpPort": 587,
    "secure": true
  }
}
```

### POST /sync

Synchronizes emails from the specified email account.

#### Request

```typescript
interface SyncEmailRequest {
  accountId: string
  options?: {
    folders?: string[] // Specific folders to sync
    since?: string // ISO date string for incremental sync
    limit?: number // Maximum emails to sync (default: 100)
    includeAttachments?: boolean // Include attachment metadata
    markAsRead?: boolean // Mark synced emails as read
  }
}
```

#### Response

```typescript
interface SyncEmailResponse {
  success: boolean
  result: {
    emailCount: number
    newEmails: number
    updatedEmails: number
    foldersProcessed: string[]
    syncDuration: number // milliseconds
    quotaUsed?: {
      apiCalls: number
      remaining: number
    }
  }
  errors?: Array<{
    folder: string
    error: string
    code: string
  }>
  nextSyncToken?: string
  lastSyncAt: string
}
```

#### Examples

**Full Sync:**
```http
POST /api/integrations/email/sync
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "accountId": "gmail_account_123",
  "options": {
    "folders": ["INBOX", "SENT"],
    "limit": 50,
    "includeAttachments": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "emailCount": 47,
    "newEmails": 12,
    "updatedEmails": 2,
    "foldersProcessed": ["INBOX", "SENT"],
    "syncDuration": 2341,
    "quotaUsed": {
      "apiCalls": 23,
      "remaining": 977
    }
  },
  "nextSyncToken": "1234567890abcdef",
  "lastSyncAt": "2025-01-03T14:30:00Z"
}
```

**Incremental Sync:**
```http
POST /api/integrations/email/sync
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "accountId": "outlook_account_456",
  "options": {
    "since": "2025-01-03T10:00:00Z",
    "limit": 25
  }
}
```

### POST /send

Sends an email using the specified email account.

#### Request

```typescript
interface SendEmailRequest {
  accountId: string
  message: {
    to: string[]
    cc?: string[]
    bcc?: string[]
    subject: string
    content: {
      text: string
      html?: string
    }
    attachments?: Array<{
      filename: string
      contentType: string
      content: string // Base64 encoded
      size: number
    }>
    headers?: Record<string, string>
    priority?: 'high' | 'normal' | 'low'
  }
  options?: {
    saveToSent?: boolean // Default: true
    trackDelivery?: boolean
    scheduleAt?: string // ISO date string for scheduled sending
    inReplyTo?: string // Message ID for threading
    references?: string[] // Message IDs for threading
  }
}
```

#### Response

```typescript
interface SendEmailResponse {
  success: boolean
  messageId?: string
  threadId?: string
  sentAt: string
  error?: string
  deliveryStatus?: {
    queued: boolean
    estimatedDelivery?: string
  }
  quotaUsed?: {
    dailyLimit: number
    remaining: number
  }
}
```

#### Examples

**Simple Email:**
```http
POST /api/integrations/email/send
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "accountId": "gmail_account_123",
  "message": {
    "to": ["<EMAIL>"],
    "subject": "Test Email",
    "content": {
      "text": "This is a test email.",
      "html": "<p>This is a <strong>test</strong> email.</p>"
    }
  }
}
```

**Email with Attachments:**
```http
POST /api/integrations/email/send
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "accountId": "outlook_account_456",
  "message": {
    "to": ["<EMAIL>"],
    "cc": ["<EMAIL>"],
    "subject": "Project Report",
    "content": {
      "text": "Please find the project report attached.",
      "html": "<p>Please find the <em>project report</em> attached.</p>"
    },
    "attachments": [
      {
        "filename": "report.pdf",
        "contentType": "application/pdf",
        "content": "JVBERi0xLjQKJdP...",
        "size": 245760
      }
    ],
    "priority": "high"
  },
  "options": {
    "trackDelivery": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "messageId": "<<EMAIL>>",
  "threadId": "16f8a2b3c4d5e6f7",
  "sentAt": "2025-01-03T14:35:22Z",
  "deliveryStatus": {
    "queued": true,
    "estimatedDelivery": "2025-01-03T14:35:30Z"
  },
  "quotaUsed": {
    "dailyLimit": 2000,
    "remaining": 1847
  }
}
```

### GET /accounts

Retrieves all email accounts for the authenticated user.

#### Request

No request body required.

#### Response

```typescript
interface EmailAccountsResponse {
  accounts: Array<{
    id: string
    provider: string
    email: string
    displayName: string
    isActive: boolean
    lastSyncAt?: string
    syncStatus: 'idle' | 'syncing' | 'error'
    quotaInfo?: {
      used: number
      total: number
      percentage: number
    }
    capabilities: string[]
  }>
  total: number
}
```

#### Example

```http
GET /api/integrations/email/accounts
Authorization: Bearer <firebase_token>
```

**Response:**
```json
{
  "accounts": [
    {
      "id": "gmail_account_123",
      "provider": "gmail",
      "email": "<EMAIL>",
      "displayName": "Personal Gmail",
      "isActive": true,
      "lastSyncAt": "2025-01-03T14:30:00Z",
      "syncStatus": "idle",
      "quotaInfo": {
        "used": **********,
        "total": ***********,
        "percentage": 12.7
      },
      "capabilities": ["oauth", "threading", "labels", "search"]
    },
    {
      "id": "outlook_account_456",
      "provider": "outlook",
      "email": "<EMAIL>",
      "displayName": "Work Outlook",
      "isActive": true,
      "lastSyncAt": "2025-01-03T14:25:00Z",
      "syncStatus": "idle",
      "capabilities": ["oauth", "folders", "calendar", "contacts"]
    }
  ],
  "total": 2
}
```

### DELETE /accounts/{accountId}

Removes an email account and all associated data.

#### Request

No request body required.

#### Response

```typescript
interface DeleteAccountResponse {
  success: boolean
  deletedAccount: {
    id: string
    email: string
    provider: string
  }
  deletedData: {
    emailCount: number
    attachmentCount: number
    sizeCleaned: number // bytes
  }
}
```

#### Example

```http
DELETE /api/integrations/email/accounts/gmail_account_123
Authorization: Bearer <firebase_token>
```

**Response:**
```json
{
  "success": true,
  "deletedAccount": {
    "id": "gmail_account_123",
    "email": "<EMAIL>",
    "provider": "gmail"
  },
  "deletedData": {
    "emailCount": 1247,
    "attachmentCount": 89,
    "sizeCleaned": ********
  }
}
```

## Rate Limiting

API rate limits vary by endpoint and provider:

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `/validate` | 10 requests | 1 minute |
| `/sync` | 20 requests | 1 hour |
| `/send` | 100 requests | 1 day |
| `/accounts` | 50 requests | 1 minute |

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 47
X-RateLimit-Reset: **********
```

## Webhooks

Configure webhooks to receive real-time notifications about email events:

### Event Types

- `email.received` - New email received
- `email.sent` - Email sent successfully
- `email.failed` - Email sending failed
- `sync.completed` - Email sync completed
- `sync.failed` - Email sync failed
- `quota.warning` - Approaching quota limit

### Webhook Payload

```typescript
interface WebhookPayload {
  event: string
  accountId: string
  userId: string
  timestamp: string
  data: {
    // Event-specific data
    emailId?: string
    error?: string
    syncStats?: SyncResult
  }
}
```

### Configuration

```http
POST /api/integrations/email/webhooks
Content-Type: application/json
Authorization: Bearer <firebase_token>

{
  "url": "https://your-app.com/webhooks/email",
  "events": ["email.received", "sync.completed"],
  "secret": "your_webhook_secret"
}
```

## SDK Usage Examples

### JavaScript/TypeScript

```typescript
import { EmailAPI } from '@pib/email-sdk'

const emailApi = new EmailAPI({
  baseUrl: 'https://your-app.com/api/integrations/email',
  authToken: firebaseIdToken
})

// Validate credentials
const validation = await emailApi.validate({
  provider: 'gmail',
  credentials: { accessToken, refreshToken }
})

// Sync emails
const syncResult = await emailApi.sync({
  accountId: 'gmail_account_123',
  options: { limit: 50 }
})

// Send email
const sendResult = await emailApi.send({
  accountId: 'gmail_account_123',
  message: {
    to: ['<EMAIL>'],
    subject: 'Test Email',
    content: { text: 'Hello World!' }
  }
})
```

### cURL Examples

```bash
# Validate Gmail credentials
curl -X POST https://your-app.com/api/integrations/email/validate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -d '{
    "provider": "gmail",
    "credentials": {
      "accessToken": "ya29.a0AfH6SMC...",
      "refreshToken": "1//04-rZ9..."
    }
  }'

# Sync emails
curl -X POST https://your-app.com/api/integrations/email/sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -d '{
    "accountId": "gmail_account_123",
    "options": {
      "limit": 25,
      "folders": ["INBOX"]
    }
  }'

# Send email
curl -X POST https://your-app.com/api/integrations/email/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -d '{
    "accountId": "gmail_account_123",
    "message": {
      "to": ["<EMAIL>"],
      "subject": "API Test",
      "content": {
        "text": "This email was sent via the PIB Email API"
      }
    }
  }'
```

## Error Scenarios

### Common Error Responses

**Invalid Credentials:**
```json
{
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Email provider authentication failed",
    "details": {
      "provider": "gmail",
      "reason": "Token expired"
    }
  },
  "timestamp": "2025-01-03T14:35:22Z",
  "path": "/api/integrations/email/validate"
}
```

**Rate Limit Exceeded:**
```json
{
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests, please try again later",
    "details": {
      "limit": 10,
      "window": "1 minute",
      "retryAfter": 45
    }
  },
  "timestamp": "2025-01-03T14:35:22Z",
  "path": "/api/integrations/email/sync"
}
```

**Provider Error:**
```json
{
  "error": {
    "code": "PROVIDER_ERROR",
    "message": "Email provider service unavailable",
    "details": {
      "provider": "outlook",
      "providerError": "Service temporarily unavailable",
      "retryable": true
    }
  },
  "timestamp": "2025-01-03T14:35:22Z",
  "path": "/api/integrations/email/send"
}
```

## Testing

### Test Environment

The API provides a test environment for development and testing:

```
Base URL: /api/integrations/email/test/
```

Test environment features:
- Mock email providers
- No actual emails sent
- Simulated delays and errors
- Test data generation

### Test Endpoints

```http
# Generate test emails
POST /api/integrations/email/test/generate
{
  "accountId": "test_account",
  "count": 10,
  "type": "inbox"
}

# Reset test data
DELETE /api/integrations/email/test/reset

# Simulate provider errors
POST /api/integrations/email/test/simulate-error
{
  "errorType": "timeout",
  "duration": 5000
}
```

---

*This API documentation is maintained alongside the PIB Email System implementation and follows OpenAPI 3.0 specifications for comprehensive integration support.*
