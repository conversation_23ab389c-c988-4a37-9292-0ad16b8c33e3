# Email Component Architecture Guide

## Overview

The PIB Email System components follow Tairo design patterns and Vue 3 Composition API conventions, providing reusable, maintainable email functionality that can be integrated throughout the application.

## Component Architecture Principles

### LEVER Compliance

- **Leverage**: Extends existing Tairo UI components and patterns
- **Extend**: Builds upon established component architecture
- **Verify**: Implements reactive patterns for real-time updates
- **Eliminate**: Reuses existing modal, input, and layout patterns
- **Reduce**: Maintains simplicity through clear component boundaries

### Design Patterns

1. **Composition Over Inheritance**: Components use composables for shared logic
2. **Single Responsibility**: Each component has a focused, well-defined purpose
3. **Reactive Data Flow**: Props down, events up pattern with reactive updates
4. **Accessibility First**: WCAG 2.1 AA compliance built into all components
5. **Mobile-First**: Responsive design with mobile-optimized interactions

## Three-Pane Component Hierarchy

```mermaid
graph TD
    A[Inbox Page - Three Pane Layout] --> B[TairoEmailSidebar.vue]
    A --> C[TairoEmailList.vue]
    A --> D[TairoEmailDetail.vue]
    A --> E[TairoEmailReply.vue]
    A --> F[TairoEmailIntegrationModal.vue]

    subgraph "Sidebar Components"
        B --> G[Account List]
        B --> H[Folder Navigation]
        B --> I[Add Account Button]
        B --> J[Sync Status Indicators]
        B --> K[Unread Count Badges]
        B --> L[Provider Icons]
    end

    subgraph "Email List Components"
        C --> M[Search Input]
        C --> N[Virtual Scroll Container]
        C --> O[Email List Items]
        C --> P[Loading States]
        C --> Q[Empty State]
    end

    subgraph "Detail Components"
        D --> R[Email Header]
        D --> S[Email Content]
        D --> T[Action Buttons]
        D --> U[Attachment List]
        D --> V[Reply Interface]
    end

    subgraph "Modals & Overlays"
        E --> W[Reply Modal]
        E --> X[Attachment Uploader]
        F --> Y[Provider Selection]
        F --> Z[Credential Forms]
        F --> AA[OAuth Handlers]
    end

    subgraph "Real-time Features"
        AB[Firebase Listeners] --> B
        AB --> C
        AB --> D
        AC[Sync Engine] --> B
        AC --> C
    end
```

## Core Components

### TairoEmailSidebar.vue

**Purpose**: The primary navigation sidebar for email account management, providing real-time account selection, folder navigation, and sync status monitoring.

#### Props Interface

```typescript
interface TairoEmailSidebarProps {
  accounts: EmailAccount[] // Array of connected email accounts
  activeAccountId?: string | null // Currently selected account ID
  loading?: boolean // Loading state for account operations
}
```

#### Events Interface

```typescript
interface TairoEmailSidebarEvents {
  'account-selected': (accountId: string) => void // Account selection change
  'add-account': () => void // Add new email account
  'sync-account': (accountId: string) => void // Manual sync trigger
  'manage-integrations': () => void // Navigate to settings
}
```

#### Usage Examples

**Basic Sidebar Implementation:**
```vue
<script setup lang="ts">
const { emailAccounts, loading: accountsLoading } = useEmailAccounts()
const activeAccountId = ref<string | null>(null)

function handleAccountSelection(accountId: string) {
  activeAccountId.value = accountId
  // This automatically triggers email list updates via reactive dependencies
}

function showIntegrationModal() {
  // Open email integration modal for adding accounts
}

async function triggerManualSync(accountId: string) {
  // Force immediate synchronization for specific account
}

function navigateToSettings() {
  navigateTo('/user/integrations')
}
</script>

<template>
  <TairoEmailSidebar
    :accounts="emailAccounts"
    :active-account-id="activeAccountId"
    :loading="accountsLoading"
    @account-selected="handleAccountSelection"
    @add-account="showIntegrationModal"
    @sync-account="triggerManualSync"
    @manage-integrations="navigateToSettings"
  />
</template>
```

#### Internal Structure

```vue
<template>
  <div class="tairo-email-sidebar">
    <!-- Header Section -->
    <div class="sidebar-header">
      <BaseHeading size="lg" weight="semibold">
        Email
      </BaseHeading>
      <BaseButton size="sm" variant="outline" @click="$emit('add-account')">
        <Icon name="lucide:plus" class="size-4" />
        Add Account
      </BaseButton>
    </div>

    <!-- Folder Navigation -->
    <div class="folder-navigation">
      <button
        v-for="folder in folders"
        :key="folder.id"
        class="folder-item"
        :class="{ active: selectedFolder === folder.id }"
        @click="selectFolder(folder.id)"
      >
        <Icon :name="folder.icon" class="size-4" />
        <span>{{ folder.name }}</span>
        <span v-if="folder.count" class="unread-badge">{{ folder.count }}</span>
      </button>
    </div>

    <!-- Account List -->
    <div class="account-list">
      <div class="account-header">
        <BaseHeading size="sm">
          Email Accounts
        </BaseHeading>
        <BaseButton size="xs" variant="ghost" @click="$emit('manage-integrations')">
          <Icon name="lucide:settings" class="size-3" />
          Manage
        </BaseButton>
      </div>

      <!-- Account Items -->
      <div
        v-for="account in accounts"
        :key="account.id"
        class="account-item"
        :class="{ active: account.id === activeAccountId }"
        @click="$emit('account-selected', account.id)"
      >
        <!-- Provider Icon with Status -->
        <div class="account-icon">
          <Icon :name="getProviderIcon(account.provider)" class="size-5" />
          <div
            class="connection-status"
            :class="account.isConnected ? 'connected' : 'disconnected'"
          />
        </div>

        <!-- Account Info -->
        <div class="account-info">
          <div class="account-name">
            {{ account.displayName }}
          </div>
          <div class="account-email">
            {{ account.email }}
          </div>
          <div class="account-status">
            {{ formatLastSync(account.lastSyncAt) }}
          </div>
        </div>

        <!-- Sync Button & Status -->
        <BaseButton
          size="xs"
          variant="ghost"
          :loading="account.syncStatus === 'syncing'"
          @click.stop="$emit('sync-account', account.id)"
        >
          <Icon
            name="lucide:refresh-cw"
            class="size-3"
            :class="{ 'animate-spin': account.syncStatus === 'syncing' }"
          />
        </BaseButton>

        <!-- Unread Count -->
        <div
          v-if="account.unreadCount && account.unreadCount > 0"
          class="unread-count"
        >
          {{ account.unreadCount > 99 ? '99+' : account.unreadCount }}
        </div>

        <!-- Storage Quota (if available) -->
        <div v-if="account.quotaInfo" class="storage-quota">
          <div class="quota-label">
            {{ account.quotaInfo.percentage }}%
          </div>
          <div class="quota-bar">
            <div
              class="quota-fill"
              :style="{ width: `${account.quotaInfo.percentage}%` }"
              :class="getQuotaColorClass(account.quotaInfo.percentage)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Actions -->
    <div class="sidebar-footer">
      <BaseButton
        variant="outline"
        size="sm"
        class="w-full"
        @click="$emit('manage-integrations')"
      >
        <Icon name="lucide:settings" class="size-4" />
        Manage Integrations
      </BaseButton>
    </div>
  </div>
</template>
```

#### Real-time Features

- **Live Account Status**: Real-time sync status updates via Firebase listeners
- **Instant Unread Counts**: Live badge updates when new emails arrive
- **Connection Monitoring**: Immediate visual feedback for connection issues
- **Cross-device Sync**: Account selection and status sync across devices
- **Smart Updates**: Efficient re-rendering using Vue's reactivity system

#### Responsive Behavior

- **Desktop (md+)**: Fixed sidebar with full account details and folder navigation
- **Tablet**: Collapsible sidebar with condensed account information
- **Mobile**: Full-screen overlay with optimized touch interactions
- **Keyboard Navigation**: Full accessibility support with focus management

### TairoEmailList.vue

**Purpose**: Displays a scrollable, searchable list of emails with selection capabilities.

#### Props Interface

```typescript
interface TairoEmailListProps {
  emails: Email[]
  loading?: boolean
  searchQuery?: string
  selectedEmailId?: string | null
  showSearch?: boolean
  compact?: boolean
  virtualScrolling?: boolean
}
```

#### Events Interface

```typescript
interface TairoEmailListEvents {
  'select-email': (emailId: string) => void
  'search': (query: string) => void
  'load-more': () => void
  'mark-read': (emailId: string) => void
  'mark-unread': (emailId: string) => void
  'delete': (emailId: string) => void
  'archive': (emailId: string) => void
}
```

#### Usage Examples

**Basic Implementation:**
```vue
<script setup lang="ts">
const { emails, loading } = useEmails()
const selectedEmailId = ref<string | null>(null)

function handleEmailSelect(emailId: string) {
  selectedEmailId.value = emailId
}

function handleSearch(query: string) {
  // Implement search logic
}
</script>

<template>
  <TairoEmailList
    :emails="emails"
    :loading="loading"
    :selected-email-id="selectedEmailId"
    @select-email="handleEmailSelect"
    @search="handleSearch"
  />
</template>
```

**Advanced Configuration:**
```vue
<template>
  <TairoEmailList
    :emails="filteredEmails"
    :loading="syncLoading"
    :selected-email-id="activeEmailId"
    :compact="isMobile"
    :virtual-scrolling="emails.length > 100"
    show-search
    @select-email="setActiveEmail"
    @search="updateSearchQuery"
    @mark-read="markEmailAsRead"
    @delete="deleteEmail"
  />
</template>
```

#### Internal Structure

```vue
<template>
  <div class="tairo-email-list">
    <!-- Search Header -->
    <div v-if="showSearch" class="email-search-header">
      <TairoInput
        v-model="searchQuery"
        icon="lucide:search"
        placeholder="Search emails..."
        @input="handleSearch"
      />
    </div>

    <!-- Email List -->
    <div class="email-list-container">
      <RecycleScroller
        v-if="virtualScrolling"
        v-slot="{ item }"
        :items="emails"
        :item-size="itemHeight"
        key-field="id"
      >
        <EmailListItem
          :email="item"
          :selected="item.id === selectedEmailId"
          :compact="compact"
          @select="$emit('select-email', item.id)"
          @action="handleAction"
        />
      </RecycleScroller>

      <div v-else class="email-list-scroll">
        <EmailListItem
          v-for="email in emails"
          :key="email.id"
          :email="email"
          :selected="email.id === selectedEmailId"
          :compact="compact"
          @select="$emit('select-email', email.id)"
          @action="handleAction"
        />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="email-list-loading">
      <BaseLoader />
    </div>

    <!-- Empty State -->
    <div v-if="!loading && emails.length === 0" class="email-list-empty">
      <EmptyState
        icon="lucide:inbox"
        title="No emails found"
        description="Your inbox is empty or no emails match your search."
      />
    </div>
  </div>
</template>
```

### TairoEmailDetail.vue

**Purpose**: Displays the full content and metadata of a selected email with action buttons.

#### Props Interface

```typescript
interface TairoEmailDetailProps {
  email: Email | null
  loading?: boolean
  canReply?: boolean
  canForward?: boolean
  canDelete?: boolean
  showAttachments?: boolean
  autoMarkRead?: boolean
}
```

#### Events Interface

```typescript
interface TairoEmailDetailEvents {
  'reply': (email: Email) => void
  'reply-all': (email: Email) => void
  'forward': (email: Email) => void
  'delete': (email: Email) => void
  'mark-read': (email: Email) => void
  'mark-unread': (email: Email) => void
  'download-attachment': (attachment: EmailAttachment) => void
  'preview-attachment': (attachment: EmailAttachment) => void
}
```

#### Usage Examples

**Basic Implementation:**
```vue
<script setup lang="ts">
const selectedEmail = computed(() =>
  emails.value.find(e => e.id === selectedEmailId.value)
)

function openReplyModal(email: Email) {
  replyModalVisible.value = true
  replyToEmail.value = email
}
</script>

<template>
  <TairoEmailDetail
    :email="selectedEmail"
    :loading="detailLoading"
    :can-reply="true"
    :can-delete="true"
    @reply="openReplyModal"
    @delete="confirmDelete"
  />
</template>
```

**Advanced Configuration:**
```vue
<template>
  <TairoEmailDetail
    :email="selectedEmail"
    :loading="detailLoading"
    :can-reply="hasReplyPermission"
    :can-forward="hasForwardPermission"
    :can-delete="hasDeletePermission"
    :show-attachments="true"
    :auto-mark-read="autoMarkReadEnabled"
    @reply="handleReply"
    @reply-all="handleReplyAll"
    @forward="handleForward"
    @delete="handleDelete"
    @download-attachment="downloadAttachment"
    @preview-attachment="previewAttachment"
  />
</template>
```

#### Internal Structure

```vue
<template>
  <div class="tairo-email-detail">
    <!-- Email Header -->
    <div class="email-header">
      <div class="email-meta">
        <BaseAvatar
          :src="email.sender.avatar"
          :alt="email.sender.name"
          size="md"
        />
        <div class="email-sender-info">
          <BaseHeading size="lg" weight="semibold">
            {{ email.sender.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            {{ email.sender.email }}
          </BaseParagraph>
        </div>
        <div class="email-timestamp">
          <BaseParagraph size="sm" class="text-muted-400">
            {{ formatDate(email.receivedAt) }}
          </BaseParagraph>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="email-actions">
        <BaseButton
          v-if="canReply"
          variant="outline"
          size="sm"
          @click="$emit('reply', email)"
        >
          <Icon name="lucide:reply" class="size-4" />
          Reply
        </BaseButton>
        <BaseButton
          v-if="canForward"
          variant="outline"
          size="sm"
          @click="$emit('forward', email)"
        >
          <Icon name="lucide:forward" class="size-4" />
          Forward
        </BaseButton>
        <BaseButton
          v-if="canDelete"
          variant="outline"
          color="danger"
          size="sm"
          @click="$emit('delete', email)"
        >
          <Icon name="lucide:trash" class="size-4" />
          Delete
        </BaseButton>
      </div>
    </div>

    <!-- Email Subject -->
    <div class="email-subject">
      <BaseHeading size="xl" weight="medium">
        {{ email.subject }}
      </BaseHeading>
    </div>

    <!-- Email Content -->
    <div class="email-content">
      <div
        v-if="email.content.html"
        class="email-html-content"
        v-html="sanitizeHtml(email.content.html)"
      />
      <div
        v-else
        class="email-text-content"
        v-text="email.content.text"
      />
    </div>

    <!-- Attachments -->
    <div v-if="showAttachments && email.attachments?.length" class="email-attachments">
      <BaseHeading size="md" class="mb-4">
        Attachments ({{ email.attachments.length }})
      </BaseHeading>
      <div class="attachment-grid">
        <EmailAttachment
          v-for="attachment in email.attachments"
          :key="attachment.id"
          :attachment="attachment"
          @download="$emit('download-attachment', attachment)"
          @preview="$emit('preview-attachment', attachment)"
        />
      </div>
    </div>
  </div>
</template>
```

### TairoEmailReply.vue

**Purpose**: Compose and send email replies with rich text editing and attachment support.

#### Props Interface

```typescript
interface TairoEmailReplyProps {
  visible: boolean
  originalEmail?: Email | null
  account: EmailAccount
  replyType?: 'reply' | 'reply-all' | 'forward'
  autoSave?: boolean
  attachmentLimit?: number
}
```

#### Events Interface

```typescript
interface TairoEmailReplyEvents {
  'update:visible': (visible: boolean) => void
  'send': (email: EmailDraft) => void
  'save-draft': (email: EmailDraft) => void
  'cancel': () => void
  'attachment-add': (file: File) => void
  'attachment-remove': (attachmentId: string) => void
}
```

#### Usage Examples

**Reply Modal:**
```vue
<script setup lang="ts">
const replyModalVisible = ref(false)
const replyToEmail = ref<Email | null>(null)
const currentAccount = ref<EmailAccount>()

async function handleSendReply(draft: EmailDraft) {
  await emailApi.send(draft)
  replyModalVisible.value = false
  toast.success('Reply sent successfully')
}
</script>

<template>
  <TairoEmailReply
    v-model:visible="replyModalVisible"
    :original-email="replyToEmail"
    :account="currentAccount"
    reply-type="reply"
    :auto-save="true"
    @send="handleSendReply"
    @save-draft="handleSaveDraft"
    @cancel="handleCancelReply"
  />
</template>
```

**Forward Modal:**
```vue
<template>
  <TairoEmailReply
    v-model:visible="forwardModalVisible"
    :original-email="emailToForward"
    :account="selectedAccount"
    reply-type="forward"
    :attachment-limit="10"
    @send="handleSendForward"
  />
</template>
```

#### Internal Structure

```vue
<template>
  <BaseModal
    :visible="visible"
    size="xl"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <BaseHeading size="lg">
        {{ getReplyTitle() }}
      </BaseHeading>
    </template>

    <div class="email-compose-form">
      <!-- Recipients -->
      <div class="form-section">
        <TairoInput
          v-model="draft.to"
          label="To"
          placeholder="<EMAIL>"
          :required="true"
          multiple
          @blur="validateRecipients"
        />
        <TairoInput
          v-if="showCcBcc || draft.cc?.length"
          v-model="draft.cc"
          label="CC"
          placeholder="<EMAIL>"
          multiple
        />
        <TairoInput
          v-if="showCcBcc || draft.bcc?.length"
          v-model="draft.bcc"
          label="BCC"
          placeholder="<EMAIL>"
          multiple
        />
        <BaseButton
          v-if="!showCcBcc"
          variant="ghost"
          size="sm"
          @click="showCcBcc = true"
        >
          Add CC/BCC
        </BaseButton>
      </div>

      <!-- Subject -->
      <div class="form-section">
        <TairoInput
          v-model="draft.subject"
          label="Subject"
          placeholder="Email subject"
          :required="true"
        />
      </div>

      <!-- Content Editor -->
      <div class="form-section">
        <EmailEditor
          v-model="draft.content"
          :placeholder="getContentPlaceholder()"
          :toolbar="editorToolbar"
          @change="handleContentChange"
        />
      </div>

      <!-- Attachments -->
      <div v-if="draft.attachments?.length || showAttachments" class="form-section">
        <EmailAttachments
          v-model="draft.attachments"
          :limit="attachmentLimit"
          @add="$emit('attachment-add', $event)"
          @remove="$emit('attachment-remove', $event)"
        />
      </div>

      <!-- Original Email -->
      <div v-if="originalEmail && replyType !== 'forward'" class="original-email">
        <Divider />
        <div class="original-email-header">
          <BaseParagraph size="sm" class="text-muted-500">
            On {{ formatDate(originalEmail.sentAt) }}, {{ originalEmail.sender.name }} wrote:
          </BaseParagraph>
        </div>
        <div class="original-email-content">
          <blockquote v-html="sanitizeHtml(originalEmail.content.html)" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer-actions">
        <BaseButton
          variant="outline"
          @click="$emit('cancel')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          v-if="autoSave"
          variant="outline"
          @click="$emit('save-draft', draft)"
        >
          Save Draft
        </BaseButton>
        <BaseButton
          variant="solid"
          :loading="sending"
          :disabled="!isValidDraft"
          @click="$emit('send', draft)"
        >
          Send
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
```

### TairoEmailIntegrationModal.vue

**Purpose**: Configure email provider integrations with provider-specific setup flows.

#### Props Interface

```typescript
interface TairoEmailIntegrationModalProps {
  visible: boolean
  provider?: EmailProviderConfig | null
  editMode?: boolean
  existingIntegration?: Integration | null
}
```

#### Events Interface

```typescript
interface TairoEmailIntegrationModalEvents {
  'update:visible': (visible: boolean) => void
  'save': (integration: Integration) => void
  'test': (credentials: EmailCredentials) => void
  'oauth-init': (provider: string) => void
  'delete': (integrationId: string) => void
}
```

#### Usage Examples

**Add New Integration:**
```vue
<script setup lang="ts">
const integrationModalVisible = ref(false)
const selectedProvider = ref<EmailProviderConfig | null>(null)

function openIntegrationModal(provider: EmailProviderConfig) {
  selectedProvider.value = provider
  integrationModalVisible.value = true
}

async function handleSaveIntegration(integration: Integration) {
  await integrationsApi.create(integration)
  integrationModalVisible.value = false
  toast.success('Email integration added successfully')
}
</script>

<template>
  <TairoEmailIntegrationModal
    v-model:visible="integrationModalVisible"
    :provider="selectedProvider"
    @save="handleSaveIntegration"
    @test="handleTestCredentials"
    @oauth-init="handleOAuthInit"
  />
</template>
```

**Edit Existing Integration:**
```vue
<template>
  <TairoEmailIntegrationModal
    v-model:visible="editModalVisible"
    :provider="integrationProvider"
    :edit-mode="true"
    :existing-integration="selectedIntegration"
    @save="handleUpdateIntegration"
    @delete="handleDeleteIntegration"
  />
</template>
```

#### Internal Structure

```vue
<template>
  <BaseModal
    :visible="visible"
    size="lg"
    @update:visible="$emit('update:visible', $event)"
  >
    <template #header>
      <div class="integration-modal-header">
        <Icon :name="provider?.icon" class="size-8" />
        <BaseHeading size="lg">
          {{ editMode ? 'Edit' : 'Add' }} {{ provider?.name }} Integration
        </BaseHeading>
      </div>
    </template>

    <div class="integration-form">
      <!-- Provider Info -->
      <div class="provider-info">
        <BaseParagraph class="text-muted-600">
          {{ provider?.description }}
        </BaseParagraph>
        <div class="provider-features">
          <BaseTag
            v-for="feature in provider?.features"
            :key="feature"
            size="sm"
            variant="outline"
          >
            {{ feature }}
          </BaseTag>
        </div>
      </div>

      <!-- OAuth Providers -->
      <div v-if="provider?.authType === 'oauth'" class="oauth-section">
        <BaseAlert
          v-if="!oauthConfigured"
          type="warning"
          title="OAuth Not Configured"
          description="OAuth credentials not configured. Please set up OAuth in your environment."
        />
        <BaseButton
          v-else
          variant="solid"
          size="lg"
          :loading="oauthLoading"
          @click="$emit('oauth-init', provider.id)"
        >
          <Icon :name="provider.icon" class="size-5" />
          Connect with {{ provider.name }}
        </BaseButton>
      </div>

      <!-- Password Providers -->
      <div v-else class="credentials-section">
        <TairoInput
          v-model="credentials.email"
          label="Email Address"
          type="email"
          placeholder="<EMAIL>"
          :required="true"
          icon="lucide:mail"
        />

        <TairoInput
          v-if="provider?.authType === 'app-password'"
          v-model="credentials.appPassword"
          label="App Password"
          type="password"
          placeholder="App-specific password"
          :required="true"
          icon="lucide:key"
        >
          <template #help>
            <BaseParagraph size="sm" class="text-muted-500">
              Generate an app password in your {{ provider.name }} settings
            </BaseParagraph>
          </template>
        </TairoInput>

        <TairoInput
          v-else
          v-model="credentials.password"
          label="Password"
          type="password"
          placeholder="Your email password"
          :required="true"
          icon="lucide:lock"
        />

        <!-- Custom Server Settings -->
        <div v-if="provider?.id === 'custom' || provider?.id === 'imap'" class="server-settings">
          <BaseHeading size="md" class="mb-4">
            Server Settings
          </BaseHeading>

          <div class="server-grid">
            <TairoInput
              v-model="credentials.imapHost"
              label="IMAP Host"
              placeholder="imap.example.com"
              :required="true"
            />
            <TairoInput
              v-model="credentials.imapPort"
              label="IMAP Port"
              type="number"
              placeholder="993"
              :required="true"
            />
            <TairoInput
              v-model="credentials.smtpHost"
              label="SMTP Host"
              placeholder="smtp.example.com"
              :required="true"
            />
            <TairoInput
              v-model="credentials.smtpPort"
              label="SMTP Port"
              type="number"
              placeholder="587"
              :required="true"
            />
          </div>

          <BaseCheckbox
            v-model="credentials.secure"
            label="Use SSL/TLS encryption"
          />
        </div>
      </div>

      <!-- Test Connection -->
      <div class="test-section">
        <BaseButton
          variant="outline"
          :loading="testing"
          :disabled="!canTest"
          @click="$emit('test', credentials)"
        >
          <Icon name="lucide:wifi" class="size-4" />
          Test Connection
        </BaseButton>

        <div v-if="testResult" class="test-result">
          <BaseAlert
            :type="testResult.success ? 'success' : 'danger'"
            :title="testResult.success ? 'Connection Successful' : 'Connection Failed'"
            :description="testResult.message"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer-actions">
        <BaseButton
          variant="outline"
          @click="$emit('update:visible', false)"
        >
          Cancel
        </BaseButton>
        <BaseButton
          v-if="editMode"
          variant="outline"
          color="danger"
          @click="confirmDelete"
        >
          Delete
        </BaseButton>
        <BaseButton
          variant="solid"
          :loading="saving"
          :disabled="!isValid"
          @click="handleSave"
        >
          {{ editMode ? 'Update' : 'Add' }} Integration
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
```

## Composables Integration

### useEmailAccounts

**Purpose**: Manage email account state and operations.

```typescript
export function useEmailAccounts() {
  const accounts = ref<EmailAccount[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const activeAccount = computed(() =>
    accounts.value.find(account => account.isActive)
  )

  const addAccount = async (credentials: EmailCredentials) => {
    // Implementation
  }

  const removeAccount = async (accountId: string) => {
    // Implementation
  }

  const syncAccount = async (accountId: string) => {
    // Implementation
  }

  return {
    accounts: readonly(accounts),
    loading: readonly(loading),
    error: readonly(error),
    activeAccount,
    addAccount,
    removeAccount,
    syncAccount
  }
}
```

### useEmails

**Purpose**: Manage email data and real-time synchronization.

```typescript
export function useEmails(accountId?: string) {
  const emails = ref<Email[]>([])
  const loading = ref(false)
  const syncing = ref(false)

  const unreadCount = computed(() =>
    emails.value.filter(email => !email.isRead).length
  )

  const filteredEmails = computed(() =>
    // Implement filtering logic
  )

  const markAsRead = async (emailId: string) => {
    // Implementation
  }

  const deleteEmail = async (emailId: string) => {
    // Implementation
  }

  const sendReply = async (draft: EmailDraft) => {
    // Implementation
  }

  return {
    emails: readonly(emails),
    loading: readonly(loading),
    syncing: readonly(syncing),
    unreadCount,
    filteredEmails,
    markAsRead,
    deleteEmail,
    sendReply
  }
}
```

## Styling Guidelines

### CSS Classes

All email components follow Tairo CSS class naming conventions:

```css
/* Component root classes */
.tairo-email-list {
}
.tairo-email-detail {
}
.tairo-email-reply {
}
.tairo-email-integration-modal {
}

/* State classes */
.email-selected {
}
.email-unread {
}
.email-loading {
}
.email-error {
}

/* Layout classes */
.email-header {
}
.email-content {
}
.email-actions {
}
.email-attachments {
}
```

### Responsive Design

```css
/* Mobile-first responsive classes */
.email-list-container {
  @apply h-full overflow-y-auto;
}

@screen md {
  .email-list-container {
    @apply h-auto;
  }
}

/* Adaptive layouts */
.email-split-view {
  @apply flex flex-col md:flex-row;
}

.email-mobile-panel {
  @apply block md:hidden;
}

.email-desktop-panel {
  @apply hidden md:block;
}
```

### Dark Mode Support

```css
/* Dark mode variants */
.email-content {
  @apply bg-white dark:bg-gray-900;
  @apply text-gray-900 dark:text-gray-100;
}

.email-unread {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.email-selected {
  @apply bg-gray-100 dark:bg-gray-800;
}
```

## Accessibility Features

### ARIA Labels

```vue
<template>
  <div
    class="tairo-email-list"
    role="listbox"
    aria-label="Email list"
    :aria-activedescendant="selectedEmailId"
  >
    <div
      v-for="email in emails"
      :id="email.id"
      :key="email.id"
      role="option"
      :aria-selected="email.id === selectedEmailId"
      :aria-label="getEmailAriaLabel(email)"
      tabindex="0"
      @click="selectEmail(email.id)"
      @keydown.enter="selectEmail(email.id)"
      @keydown.space.prevent="selectEmail(email.id)"
    >
      <!-- Email content -->
    </div>
  </div>
</template>
```

### Keyboard Navigation

```typescript
// Keyboard navigation implementation
function handleKeydown(event: KeyboardEvent) {
  switch (event.key) {
    case 'ArrowDown':
      selectNextEmail()
      break
    case 'ArrowUp':
      selectPreviousEmail()
      break
    case 'Enter':
    case ' ':
      openSelectedEmail()
      break
    case 'Delete':
      deleteSelectedEmail()
      break
    case 'r':
      if (event.ctrlKey || event.metaKey) {
        replyToSelectedEmail()
      }
      break
  }
}
```

### Screen Reader Support

```vue
<script setup lang="ts">
function getStatusMessage() {
  if (loading.value)
    return 'Loading emails...'
  if (syncing.value)
    return 'Synchronizing emails...'
  return `${emails.value.length} emails, ${unreadCount.value} unread`
}

function getAnnouncementMessage() {
  // Return important messages for screen readers
}
</script>

<template>
  <div class="email-status" aria-live="polite">
    {{ getStatusMessage() }}
  </div>

  <div class="email-announcements" aria-live="assertive">
    {{ getAnnouncementMessage() }}
  </div>
</template>
```

## Performance Optimization

### Virtual Scrolling

```vue
<template>
  <RecycleScroller
    v-if="emails.length > 100"
    v-slot="{ item }"
    class="email-scroller"
    :items="emails"
    :item-size="80"
    key-field="id"
  >
    <EmailListItem :email="item" />
  </RecycleScroller>
</template>
```

### Lazy Loading

```typescript
// Implement intersection observer for lazy loading
function useIntersectionObserver() {
  const loadMore = () => {
    if (!loading.value && hasNextPage.value) {
      fetchNextPage()
    }
  }

  const observerCallback = (entries: IntersectionObserverEntry[]) => {
    if (entries[0].isIntersecting) {
      loadMore()
    }
  }

  return { observerCallback }
}
```

### Memoization

```typescript
// Memoize expensive computations
const processedEmails = computed(() => {
  return emails.value.map(email => ({
    ...email,
    searchableText: `${email.subject} ${email.sender.name} ${email.content.text}`.toLowerCase(),
    displayDate: formatRelativeDate(email.receivedAt)
  }))
})
```

## Testing Components

### Unit Tests

```typescript
describe('TairoEmailList', () => {
  it('should render emails correctly', () => {
    const wrapper = mount(TairoEmailList, {
      props: {
        emails: mockEmails,
        loading: false
      }
    })

    expect(wrapper.find('.tairo-email-list')).toBeTruthy()
    expect(wrapper.findAll('[role="option"]')).toHaveLength(mockEmails.length)
  })

  it('should emit select-email event on click', async () => {
    const wrapper = mount(TairoEmailList, {
      props: { emails: mockEmails }
    })

    await wrapper.find('[role="option"]').trigger('click')

    expect(wrapper.emitted('select-email')).toBeTruthy()
    expect(wrapper.emitted('select-email')[0]).toEqual([mockEmails[0].id])
  })
})
```

### Integration Tests

```typescript
describe('Email Component Integration', () => {
  it('should handle email selection flow', async () => {
    const wrapper = mount(EmailInbox, {
      global: {
        stubs: ['TairoEmailList', 'TairoEmailDetail']
      }
    })

    // Simulate email selection
    await wrapper.findComponent(TairoEmailList).vm.$emit('select-email', 'email-1')

    // Verify detail view updates
    expect(wrapper.findComponent(TairoEmailDetail).props('email')).toBeTruthy()
  })
})
```

---

*This component documentation provides comprehensive guidance for implementing, extending, and maintaining the PIB Email System components following BMAD development standards and Tairo design patterns.*
