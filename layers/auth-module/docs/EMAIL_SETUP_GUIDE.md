# Email System Setup Guide

## Overview

This comprehensive guide walks you through setting up the PIB Email System, from initial configuration to adding email accounts and managing your inbox. The system supports multiple email providers with secure authentication and real-time synchronization.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Initial Setup](#initial-setup)
3. [Environment Configuration](#environment-configuration)
4. [Firebase Configuration](#firebase-configuration)
5. [Email Provider Setup](#email-provider-setup)
6. [Adding Email Accounts](#adding-email-accounts)
7. [Managing Your Inbox](#managing-your-inbox)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Node.js**: Version 22 or higher
- **pnpm**: Latest version (workspaces support)
- **Firebase Project**: With Authentication and Firestore enabled
- **Email Accounts**: Supported provider accounts with proper access

### Supported Email Providers

| Provider | Auth Method | Requirements |
|----------|-------------|--------------|
| **Gmail** | OAuth 2.0 | Google Cloud Console project |
| **Outlook** | OAuth 2.0 | Microsoft Azure app registration |
| **Yahoo Mail** | App Password | Yahoo account with 2FA enabled |
| **Exchange** | Password | Exchange server credentials |
| **IMAP/SMTP** | Password | Custom server configuration |

## Initial Setup

### 1. Install Dependencies

```bash
# Navigate to project root
cd /path/to/pib

# Install all dependencies
pnpm install

# Verify email module dependencies
pnpm --filter=@pib/auth-module install
```

### 2. Verify Project Structure

Ensure the email system files are properly installed:

```bash
# Check component files
ls -la layers/auth-module/components/email/

# Check API endpoints
ls -la layers/auth-module/server/api/integrations/email/

# Check configuration files
ls -la layers/auth-module/config/email-providers.ts
```

Expected files:
- `TairoEmailList.vue`
- `TairoEmailDetail.vue`
- `TairoEmailReply.vue`
- `TairoEmailIntegrationModal.vue`
- Email API endpoints (`validate.post.ts`, `sync.post.ts`, `send.post.ts`)
- Email provider configurations

## Environment Configuration

### 1. Create Environment File

Create or update your `.env` file in the project root:

```bash
# Copy environment template
cp .env.example .env

# Or create new environment file
touch .env
```

### 2. Add Email Configuration

Add the following environment variables to your `.env` file:

```bash
# Firebase Configuration (if not already set)
NUXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NUXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NUXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NUXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NUXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Email Provider OAuth Credentials
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_oauth_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_oauth_client_secret

# Encryption & Security
ENCRYPTION_KEY=your_32_character_encryption_key_here
NEXTAUTH_SECRET=your_nextauth_secret_key

# Development Settings
NUXT_PUBLIC_USE_FIREBASE_EMULATOR=true
NODE_ENV=development
```

### 3. Generate Encryption Key

Generate a secure encryption key for credential storage:

```bash
# Generate random 32-character key
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"

# Or use openssl
openssl rand -hex 16
```

## Firebase Configuration

### 1. Enable Authentication Providers

In your Firebase Console:

1. Navigate to **Authentication > Sign-in method**
2. Enable **Google** provider
3. Enable **Microsoft** provider
4. Configure OAuth redirect URLs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

### 2. Configure Firestore Database

#### Security Rules

Update your `firestore.rules` file:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Email accounts collection
    match /email_accounts/{accountId} {
      allow read, write: if request.auth != null
        && request.auth.uid == resource.data.userId
        && validateEmailAccount(request.resource.data);
    }

    // Email messages collection
    match /email_messages/{messageId} {
      allow read, write: if request.auth != null
        && request.auth.uid == resource.data.userId
        && exists(/databases/$(database)/documents/email_accounts/$(resource.data.accountId));
    }

    // Email attachments collection
    match /email_attachments/{attachmentId} {
      allow read, write: if request.auth != null
        && request.auth.uid == resource.data.userId;
    }

    // Validation functions
    function validateEmailAccount(data) {
      return data.keys().hasAll(['userId', 'provider', 'email', 'credentials'])
        && data.userId is string
        && data.provider in ['gmail', 'outlook', 'yahoo', 'imap', 'exchange', 'custom']
        && data.email is string
        && data.credentials is map;
    }
  }
}
```

#### Deploy Rules

```bash
# Deploy Firestore rules
pnpm deploy:rules

# Or use Firebase CLI directly
firebase deploy --only firestore:rules
```

### 3. Create Indexes

Create necessary Firestore indexes:

```bash
# Create firestore.indexes.json
cat > firestore.indexes.json << EOF
{
  "indexes": [
    {
      "collectionGroup": "email_messages",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "accountId", "order": "ASCENDING"},
        {"fieldPath": "receivedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "email_messages",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "isRead", "order": "ASCENDING"},
        {"fieldPath": "receivedAt", "order": "DESCENDING"}
      ]
    }
  ],
  "fieldOverrides": []
}
EOF

# Deploy indexes
firebase deploy --only firestore:indexes
```

## Email Provider Setup

### Gmail OAuth Setup

#### 1. Google Cloud Console Configuration

1. Visit [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable **Gmail API**:
   - Navigate to **APIs & Services > Library**
   - Search for "Gmail API" and enable it

#### 2. OAuth Consent Screen

1. Navigate to **APIs & Services > OAuth consent screen**
2. Configure consent screen:
   - **Application name**: PIB Email System
   - **User support email**: Your email
   - **Scopes**: Add Gmail scopes:
     - `https://www.googleapis.com/auth/gmail.readonly`
     - `https://www.googleapis.com/auth/gmail.send`
     - `https://www.googleapis.com/auth/gmail.modify`

#### 3. Create OAuth Credentials

1. Navigate to **APIs & Services > Credentials**
2. Click **Create Credentials > OAuth 2.0 Client IDs**
3. Configure:
   - **Application type**: Web application
   - **Name**: PIB Email Integration
   - **Authorized redirect URIs**:
     - `http://localhost:3000/api/integrations/email/oauth/callback` (development)
     - `https://yourdomain.com/api/integrations/email/oauth/callback` (production)

#### 4. Update Environment Variables

```bash
# Add to .env file
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

### Outlook OAuth Setup

#### 1. Microsoft Azure Portal Configuration

1. Visit [Azure Portal](https://portal.azure.com/)
2. Navigate to **Azure Active Directory > App registrations**
3. Click **New registration**

#### 2. App Registration

Configure your app registration:
- **Name**: PIB Email System
- **Supported account types**: Accounts in any organizational directory and personal Microsoft accounts
- **Redirect URI**: Web - `http://localhost:3000/api/integrations/email/oauth/callback`

#### 3. API Permissions

1. Navigate to **API permissions**
2. Add permissions:
   - **Microsoft Graph**:
     - `Mail.Read` (Delegated)
     - `Mail.Send` (Delegated)
     - `Mail.ReadWrite` (Delegated)
3. Grant admin consent

#### 4. Client Secret

1. Navigate to **Certificates & secrets**
2. Click **New client secret**
3. Copy the secret value immediately

#### 5. Update Environment Variables

```bash
# Add to .env file
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
```

### Yahoo Mail Setup

Yahoo Mail requires app-specific passwords:

#### 1. Enable Two-Factor Authentication

1. Visit [Yahoo Account Security](https://login.yahoo.com/account/security)
2. Enable **Two-step verification**

#### 2. Generate App Password

1. Navigate to **Account Security > Generate app password**
2. Select **Other app** and name it "PIB Email"
3. Copy the generated 16-character password

#### 3. Configuration

No environment variables needed - users enter app password during setup.

## Adding Email Accounts

### 1. Access Integrations Page

1. Navigate to your PIB application
2. Go to **Settings > Integrations**
3. Scroll to the **Email** section

### 2. Gmail Account Setup

#### Step-by-Step Process

1. Click **Add Integration** in Email section
2. Select **Gmail** provider
3. Click **Connect with Gmail**
4. Complete OAuth authorization:
   - Sign in to your Google account
   - Grant requested permissions
   - Return to PIB application
5. Verify connection and save

#### Verification

- Check connection status indicator
- Verify account appears in email accounts list
- Test sync functionality

### 3. Outlook Account Setup

#### Step-by-Step Process

1. Click **Add Integration** in Email section
2. Select **Outlook** provider
3. Click **Connect with Outlook**
4. Complete Microsoft OAuth flow:
   - Sign in to your Microsoft account
   - Grant requested permissions
   - Return to PIB application
5. Verify connection and save

### 4. Yahoo Mail Setup

#### Step-by-Step Process

1. Click **Add Integration** in Email section
2. Select **Yahoo Mail** provider
3. Enter configuration:
   - **Email Address**: <EMAIL>
   - **App Password**: 16-character password from Yahoo
4. Click **Test Connection** to verify
5. Save integration

### 5. Custom IMAP/SMTP Setup

#### Step-by-Step Process

1. Click **Add Integration** in Email section
2. Select **IMAP Server** or **Custom Email Server**
3. Enter server configuration:
   - **Email Address**: <EMAIL>
   - **Password**: Your email password
   - **IMAP Host**: imap.domain.com
   - **IMAP Port**: 993 (usually)
   - **SMTP Host**: smtp.domain.com
   - **SMTP Port**: 587 or 465
   - **Use SSL/TLS**: Enable for secure connection
4. Click **Test Connection**
5. Save configuration

#### Common IMAP/SMTP Settings

| Provider | IMAP Host | IMAP Port | SMTP Host | SMTP Port |
|----------|-----------|-----------|-----------|-----------|
| **Gmail** | imap.gmail.com | 993 | smtp.gmail.com | 587 |
| **Outlook** | outlook.office365.com | 993 | smtp.office365.com | 587 |
| **Yahoo** | imap.mail.yahoo.com | 993 | smtp.mail.yahoo.com | 587 |
| **iCloud** | imap.mail.me.com | 993 | smtp.mail.me.com | 587 |

## Using the Three-Pane Email Interface

### 1. Accessing Your Inbox

1. Navigate to the **Inbox** page in your PIB application
2. The three-pane layout will display:
   - **Left Sidebar**: Email accounts and folder navigation
   - **Center Panel**: Email list with search and filtering
   - **Right Panel**: Selected email details and actions

### 2. Account Management via Sidebar

#### Adding Your First Email Account
1. Click **"Add Account"** button in the sidebar
2. Select your email provider (Gmail, Outlook, Yahoo, etc.)
3. Complete the authentication flow
4. Account appears in sidebar with real-time sync status

#### Switching Between Accounts
1. View all connected accounts in the left sidebar
2. Click any account to switch and view its emails
3. Active account is highlighted with visual indicator
4. Unread counts are displayed for each account
5. Sync status shows with colored indicators:
   - **Green**: Successfully connected and synced
   - **Orange**: Currently syncing
   - **Red**: Sync error or connection issue

#### Account Status Indicators
- **Connection Status**: Colored dots show account health
- **Unread Badges**: Live count of unread emails per account
- **Sync Status**: Visual feedback for ongoing operations
- **Last Sync Time**: Shows when account was last synchronized
- **Storage Quota**: Progress bar for supported providers (Gmail, Outlook)

### 3. Real-Time Email Synchronization

#### Automatic Background Sync
- **Frequency**: Every 5 minutes for all connected accounts
- **Live Updates**: New emails appear instantly without page refresh
- **Cross-Device Sync**: Changes sync across all your devices and browser tabs
- **Smart Sync**: Only downloads new and changed emails to minimize bandwidth

#### Manual Sync Options
1. **Individual Account Sync**: Click the sync button (⟳) next to any account in the sidebar
2. **Force Refresh**: Use browser refresh or keyboard shortcut for full reload
3. **Reconnect Account**: Available when sync errors occur

#### Sync Status Understanding
- **Idle (Green dot)**: Account connected and up-to-date
- **Syncing (Orange dot)**: Currently downloading emails
- **Error (Red dot)**: Connection or authentication problem
- **Last Sync Time**: Shows "Just now", "2m ago", "1h ago", etc.

### 4. Integration with Application Settings

#### Managing Accounts via Settings
1. Click **"Manage Integrations"** button in sidebar
2. Navigate to `/user/integrations` page
3. Scroll to **"Email Accounts"** section
4. Add, edit, or remove email provider connections

#### Seamless Account Setup Flow
1. **From Inbox**: Click "Add Account" → Opens integration modal
2. **From Settings**: Navigate to integrations → Select email provider
3. **OAuth Providers** (Gmail, Outlook):
   - Click "Connect" → Browser OAuth flow → Automatic setup
4. **Password Providers** (Yahoo, IMAP):
   - Enter credentials → Test connection → Save configuration

#### Account Management Features
- **Real-time Status**: Connection health shown in both inbox and settings
- **Edit Credentials**: Update passwords and server settings
- **Remove Accounts**: Delete with automatic data cleanup
- **Default Account**: Set primary account for new email composition
- **Account Details**: View provider info, last sync, and storage usage

### 5. Using the Email List (Center Panel)

#### Real-Time Email Feed
- **Live Updates**: New emails appear instantly with subtle animations
- **Smart Sorting**: Emails sorted by received date (newest first)
- **Read Status**: Visual distinction between read and unread emails
- **Search-as-You-Type**: Instant filtering across all email content
- **Infinite Scroll**: Automatic loading of older emails as you scroll

#### Email Status Indicators
- **Unread emails**: Bold text with blue accent indicator
- **Read emails**: Normal text with subtle opacity
- **Starred emails**: Gold star icon for important messages
- **Attachments**: Paperclip icon for emails with files
- **Reply Indicators**: Threading indicators for conversation context

#### Email Detail

1. Click any email in the list to view details
2. View full email content, including HTML formatting
3. Download or preview attachments
4. Use action buttons for reply, forward, delete

### 4. Composing and Replying

#### Reply to Email

1. Open email in detail view
2. Click **Reply** or **Reply All** button
3. Compose your response in the reply modal
4. Add attachments if needed
5. Click **Send**

#### Forward Email

1. Open email in detail view
2. Click **Forward** button
3. Enter recipient email addresses
4. Add your message
5. Click **Send**

#### Compose New Email

1. Click **Compose** button in inbox
2. Select sending account
3. Enter recipients, subject, and content
4. Add attachments if needed
5. Send or save as draft

### 5. Email Organization

#### Filtering Options

- **All Mail**: View all emails
- **Inbox**: View only inbox emails
- **Sent**: View sent emails
- **Drafts**: View draft emails
- **Unread**: View only unread emails
- **Starred**: View starred/important emails

#### Search Functionality

Use the search bar to find emails by:
- **Sender name or email**
- **Subject keywords**
- **Email content**
- **Date ranges**

#### Managing Email Status

- **Mark as Read/Unread**: Click status indicator
- **Delete**: Click delete button or use keyboard shortcut
- **Archive**: Move emails to archive folder
- **Star**: Mark important emails with star

## Troubleshooting

### Common Issues

#### OAuth Authentication Failures

**Problem**: "Authorization failed" or "Invalid client" errors

**Solutions**:
1. Verify OAuth credentials in environment variables
2. Check redirect URLs in provider console
3. Ensure OAuth consent screen is properly configured
4. Verify scopes are correctly requested

**Debug Steps**:
```bash
# Check environment variables
echo $GOOGLE_CLIENT_ID
echo $MICROSOFT_CLIENT_ID

# Verify OAuth endpoints
curl -i "https://accounts.google.com/o/oauth2/v2/auth?client_id=$GOOGLE_CLIENT_ID&response_type=code"
```

#### Email Sync Issues

**Problem**: Emails not synchronizing or appearing in inbox

**Solutions**:
1. Check internet connectivity
2. Verify email account credentials
3. Review Firebase security rules
4. Check API quotas and rate limits
5. Examine console logs for errors

**Debug Steps**:
```bash
# Check sync status
curl -X POST "http://localhost:3000/api/integrations/email/sync" \
  -H "Authorization: Bearer $FIREBASE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"accountId": "your_account_id"}'

# Check Firebase logs
firebase functions:log
```

#### Connection Timeouts

**Problem**: IMAP/SMTP connection timeouts

**Solutions**:
1. Verify server hostnames and ports
2. Check firewall settings
3. Try alternative ports (587 vs 465 for SMTP)
4. Enable/disable SSL/TLS as needed
5. Contact email provider support

**Testing Connection**:
```bash
# Test IMAP connection
telnet imap.gmail.com 993

# Test SMTP connection
telnet smtp.gmail.com 587
```

#### Performance Issues

**Problem**: Slow email loading or interface lag

**Solutions**:
1. Enable virtual scrolling for large email lists
2. Implement email pagination
3. Optimize database queries
4. Use email caching strategies
5. Consider background sync jobs

**Performance Monitoring**:
```typescript
// Add to composables/useEmails.ts
const performanceMetrics = {
  syncDuration: 0,
  emailCount: 0,
  apiCalls: 0
}

function trackSyncPerformance(startTime: number, emailCount: number) {
  performanceMetrics.syncDuration = Date.now() - startTime
  performanceMetrics.emailCount = emailCount
  console.log('Sync performance:', performanceMetrics)
}
```

### Error Messages and Solutions

#### "Invalid credentials"

- **Gmail/Outlook**: Re-authorize OAuth connection
- **IMAP/SMTP**: Verify username/password
- **Yahoo**: Generate new app password

#### "Rate limit exceeded"

- **Gmail**: Check Google API quota usage
- **Outlook**: Review Microsoft Graph rate limits
- **IMAP**: Reduce sync frequency

#### "Connection refused"

- **Network**: Check internet connectivity
- **Firewall**: Verify outbound ports are open
- **Server**: Try alternative server settings

#### "Permission denied"

- **OAuth**: Re-grant permissions in provider console
- **Firebase**: Check Firestore security rules
- **Account**: Verify account has proper access

### Getting Help

#### Support Resources

1. **Documentation**: Refer to comprehensive system documentation
2. **Community**: Join PIB development community
3. **Issues**: Report bugs on GitHub repository
4. **Email**: Contact support team

#### Debug Information

When reporting issues, include:

```bash
# System information
node --version
pnpm --version
firebase --version

# Environment check
echo "Node ENV: $NODE_ENV"
echo "Firebase Project: $NUXT_PUBLIC_FIREBASE_PROJECT_ID"

# Recent logs
pnpm dev --verbose
```

#### Useful Commands

```bash
# Reset email data
firebase firestore:delete --recursive /email_messages

# Clear Firebase cache
firebase functions:shell

# Restart development server
pnpm dev --clear-cache

# Test API endpoints
curl -X GET "http://localhost:3000/api/integrations/email/accounts" \
  -H "Authorization: Bearer $FIREBASE_TOKEN"
```

---

*This setup guide provides comprehensive instructions for configuring and using the PIB Email System. For additional support, refer to the technical documentation or contact the development team.*
