# Email System Documentation

## Overview

The PIB Email System is a comprehensive, real-time email management platform featuring a three-pane layout with live synchronization, unified inbox management, and seamless integration with the application settings system. The system provides a fully functional email client with multi-provider support, real-time updates, and secure credential management.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Component Structure](#component-structure)
3. [Email Provider Configuration](#email-provider-configuration)
4. [API Endpoints](#api-endpoints)
5. [Security & Authentication](#security--authentication)
6. [Setup & Configuration](#setup--configuration)
7. [Usage Guide](#usage-guide)
8. [Testing Strategy](#testing-strategy)
9. [Troubleshooting](#troubleshooting)

## Architecture Overview

The email system follows LEVER principles:
- **Leverage**: Extends existing inbox UI and integration patterns
- **Extend**: Builds on Firebase auth and component architecture
- **Verify**: Implements reactive patterns for real-time updates
- **Eliminate**: Reuses existing encryption and validation patterns
- **Reduce**: Maintains simplicity while adding powerful functionality

### Three-Pane Email Interface Architecture

The system implements a modern three-pane email client with real-time synchronization:

```mermaid
graph TB
    A[Three-Pane Layout] --> B[TairoEmailSidebar]
    A --> C[TairoEmailList]
    A --> D[TairoEmailDetail]

    B --> E[Account Selection]
    B --> F[Folder Navigation]
    B --> G[Sync Status Indicators]
    B --> H[Unread Count Badges]

    C --> I[Real-time Email Feed]
    C --> J[Search & Filtering]
    C --> K[Virtual Scrolling]
    C --> L[Selection Management]

    D --> M[Email Content Display]
    D --> N[Action Buttons]
    D --> O[Attachment Handling]
    D --> P[Reply/Forward Modals]

    Q[Real-time Sync Engine] --> R[Firebase Listeners]
    Q --> S[Provider APIs]
    Q --> T[Cross-device Sync]

    R --> I
    R --> E
    R --> G

    subgraph "Integration Layer"
        U[Settings Integration]
        V[Account Management]
        W[OAuth Handlers]
    end

    B --> U
    U --> V
    V --> W

    subgraph "Provider Support"
        S1[Gmail OAuth 2.0]
        S2[Outlook Graph API]
        S3[Yahoo App Passwords]
        S4[Exchange Server]
        S5[IMAP/SMTP Servers]
        S6[Custom Email Servers]
    end

    S --> S1
    S --> S2
    S --> S3
    S --> S4
    S --> S5
    S --> S6
```

### Real-Time Synchronization Flow

```mermaid
sequenceDiagram
    participant U as User
    participant S as Sidebar
    participant L as Email List
    participant F as Firebase
    participant P as Provider APIs

    Note over U,P: Initial Load
    U->>S: Open Inbox
    S->>F: Subscribe to email accounts
    F->>S: Return connected accounts
    S->>U: Display account list with status

    Note over U,P: Account Selection
    U->>S: Select Email Account
    S->>F: Subscribe to account emails
    F->>L: Stream real-time emails
    L->>U: Display emails instantly

    Note over U,P: Background Sync (Every 5 minutes)
    loop Auto Sync
        P->>F: Push new email data
        F->>L: Live email notifications
        F->>S: Update unread counts
        L->>U: New emails appear without refresh
    end

    Note over U,P: Manual Sync
    U->>S: Click sync button
    S->>P: Force immediate sync
    P->>F: Updated email data
    F->>L: Instant email updates
    F->>S: Updated sync status
```

## Component Structure

### TairoEmailSidebar.vue

**Purpose**: Mailbox sidebar providing email account selection, folder navigation, and sync management.

**Props**:
- `accounts: EmailAccount[]` - Array of connected email accounts
- `activeAccountId: string | null` - Currently selected account ID
- `loading: boolean` - Loading state for account operations

**Events**:
- `@account-selected(accountId: string)` - Emitted when user selects different account
- `@add-account()` - Emitted when user wants to add new email account
- `@sync-account(accountId: string)` - Emitted when user triggers manual sync
- `@manage-integrations()` - Emitted when user navigates to integrations settings

**Features**:
- **Real-time Account Status**: Live sync status indicators (idle, syncing, error)
- **Unread Count Badges**: Live unread email counts per account
- **Provider Icons**: Gmail, Outlook, Yahoo, Exchange, IMAP, Custom icons
- **Sync Management**: Manual sync buttons with loading states
- **Folder Navigation**: Inbox, Sent, Drafts, Starred, Trash folders
- **Storage Quota**: Visual quota indicators for supported providers
- **Connection Health**: Real-time connection status monitoring
- **Responsive Design**: Collapsible on mobile, persistent on desktop

**Real-time Features**:
- Firebase Firestore listeners for account status updates
- Live unread count synchronization across devices
- Instant sync status changes
- Cross-device account activation

### TairoEmailList.vue

**Purpose**: Real-time email list with live updates, advanced search, and infinite scrolling.

**Props**:
- `messages: EmailMessage[]` - Array of email messages (live from Firebase)
- `activeMessageId: string | null` - Currently selected email ID
- `loading: boolean` - Initial loading state indicator
- `syncing: boolean` - Sync operation indicator
- `searchQuery: string` - Search query for filtering (v-model)

**Events**:
- `@message-selected(emailId: string)` - Emitted when an email is selected
- `@panel-activated()` - Emitted when mobile detail panel is activated
- `@update:search(query: string)` - Emitted when search query changes

**Real-time Features**:
- **Live Email Updates**: New emails appear instantly without refresh
- **Real-time Status Changes**: Read/unread status updates across devices
- **Live Search Results**: Instant search filtering as user types
- **Sync Status Indicators**: Visual feedback for ongoing sync operations
- **Unread Count Updates**: Live badge updates for unread emails
- **Cross-device Synchronization**: Selection and status sync across devices

**Enhanced Features**:
- **Virtual Scrolling**: Efficient rendering for large email lists (1000+ emails)
- **Infinite Loading**: Automatic pagination with load-more functionality
- **Advanced Search**: Full-text search across subject, sender, and content
- **Smart Filtering**: Filter by read status, starred, attachments, date ranges
- **Responsive Design**: Adaptive layout for mobile/desktop
- **Keyboard Navigation**: Full keyboard support with shortcuts
- **Accessibility**: WCAG 2.1 AA compliance with screen reader support

### TairoEmailDetail.vue

**Purpose**: Displays detailed view of a selected email with actions and content.

**Props**:
- `email: Email | null` - Email object to display
- `loading: boolean` - Loading state for email content
- `canReply: boolean` - Whether reply actions are available

**Events**:
- `@reply` - Emitted when reply action is triggered
- `@forward` - Emitted when forward action is triggered
- `@delete` - Emitted when delete action is triggered
- `@mark-read` - Emitted when marking email as read

**Features**:
- HTML and plain text email rendering
- Attachment download and preview
- Action buttons (reply, forward, delete, mark as read)
- Responsive layout with proper content formatting

### TairoEmailReply.vue

**Purpose**: Compose and send email replies with attachment support.

**Props**:
- `originalEmail: Email | null` - Original email being replied to
- `account: EmailAccount` - Email account to send from
- `visible: boolean` - Modal visibility state

**Events**:
- `@send` - Emitted when reply is sent
- `@cancel` - Emitted when reply is cancelled
- `@update:visible` - Emitted when modal visibility changes

**Features**:
- Rich text editor with formatting options
- Attachment upload and management
- Email validation and error handling
- Auto-save draft functionality

### TairoEmailIntegrationModal.vue

**Purpose**: Configure and manage email provider integrations.

**Props**:
- `visible: boolean` - Modal visibility state
- `provider: EmailProvider | null` - Email provider to configure

**Events**:
- `@close` - Emitted when modal is closed
- `@save` - Emitted when integration is saved
- `@update:visible` - Emitted when modal visibility changes

**Features**:
- Provider-specific configuration forms
- OAuth flow initiation for supported providers
- Credential validation and testing
- Error handling and user feedback

## Email Provider Configuration

### Supported Providers

| Provider | Auth Type | Features | OAuth Required |
|----------|-----------|----------|----------------|
| Gmail | OAuth2 | Full API access, threading, labels | Yes |
| Outlook | OAuth2 | Graph API, advanced features | Yes |
| Yahoo | App Password | IMAP/SMTP access | No |
| Exchange | Password | Enterprise features | No |
| IMAP | Password | Custom server support | No |
| Custom | Password | Fully configurable | No |

### Provider Configuration Examples

#### Gmail OAuth Configuration

```typescript
const gmailConfig: EmailProviderConfig = {
  id: 'gmail',
  name: 'Gmail',
  authType: 'oauth',
  oauthConfig: {
    authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenUrl: 'https://oauth2.googleapis.com/token',
    scopes: [
      'https://www.googleapis.com/auth/gmail.readonly',
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/gmail.modify'
    ],
    clientIdEnvVar: 'GOOGLE_CLIENT_ID',
    clientSecretEnvVar: 'GOOGLE_CLIENT_SECRET'
  },
  features: [
    'OAuth2 Authentication',
    'Full Email Sync',
    'Send Email',
    'Thread Management',
    'Label Support'
  ]
}
```

#### Custom IMAP Configuration

```typescript
const imapConfig: EmailProviderConfig = {
  id: 'imap',
  name: 'IMAP Server',
  authType: 'password',
  serverConfig: {
    imapHost: 'mail.example.com',
    imapPort: 993,
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    secure: true
  },
  features: [
    'Custom IMAP/SMTP Configuration',
    'Username/Password Authentication',
    'Full Email Sync',
    'Send Email'
  ]
}
```

## Real-Time Features & Synchronization

### Live Email Synchronization

The email system provides real-time updates through Firebase Firestore listeners, ensuring users see email changes instantly across all devices and browser tabs.

#### Automatic Background Sync
- **Frequency**: Every 5 minutes for all connected accounts
- **Scope**: New emails, status changes, folder updates
- **Efficiency**: Incremental sync to minimize API usage
- **Conflict Resolution**: Last-write-wins with timestamp comparison

#### Manual Sync Operations
Users can trigger immediate synchronization via:
- Sidebar sync buttons per account
- Global refresh action
- Account re-connection after errors

#### Real-Time Data Flow

```mermaid
graph LR
    A[Email Provider] --> B[Sync API]
    B --> C[Firebase Firestore]
    C --> D[Real-time Listeners]
    D --> E[Vue Components]
    E --> F[UI Updates]

    G[User Action] --> H[Component Event]
    H --> I[Composable Method]
    I --> J[API Call]
    J --> C

    subgraph "Live Updates"
        C --> K[New Email Notifications]
        C --> L[Status Change Events]
        C --> M[Unread Count Updates]
    end

    K --> E
    L --> E
    M --> E
```

### Cross-Device Synchronization

#### Account State Sync
- **Active Account**: Synced across devices using Firebase
- **Email Selection**: Current selection maintained per device
- **Read Status**: Synchronized in real-time
- **Folder Navigation**: Current folder state maintained

#### Conflict Resolution
- **Read Status**: Last action wins
- **Email Selection**: Device-local state
- **Account Switching**: Real-time propagation
- **Sync Status**: Immediate updates

### Enhanced Composables

#### useEmailAccounts() - Real-Time Account Management

**Real-time Capabilities**:
```typescript
// Automatic account status monitoring
const {
  emailAccounts, // Live account list
  loading, // Global loading state
  syncEmailAccount, // Manual sync trigger
  setDefaultEmailAccount // Real-time default updates
} = useEmailAccounts()

// Real-time account status updates
watchEffect(() => {
  emailAccounts.value.forEach((account) => {
    console.log(`${account.email}: ${account.syncStatus}`)
    // 'idle' | 'syncing' | 'error'
  })
})
```

**Features**:
- Firebase Firestore real-time listeners
- Automatic account discovery
- Live sync status tracking
- Cross-device account management
- Background sync coordination

#### useEmails() - Live Email Management

**Real-time Capabilities**:
```typescript
// Live email feed for specific account
const {
  emails, // Real-time email array
  loading, // Initial load state
  syncing, // Sync operation state
  unreadCount, // Live unread count
  markEmailAsRead, // Instant status updates
  deleteEmail, // Immediate removal
  sendReply // Real-time sent status
} = useEmails(accountId)

// Automatic updates when emails change
watchEffect(() => {
  console.log(`Unread emails: ${unreadCount.value}`)
  // Updates instantly when new emails arrive
})
```

**Features**:
- Real-time email streaming
- Live search result updates
- Instant status change propagation
- Cross-device email synchronization
- Automatic pagination with virtual scrolling

### Integration with Settings System

#### Seamless Account Management
- **Add Accounts**: Directly from inbox sidebar → integrations page
- **Manage Accounts**: Settings integration for account configuration
- **OAuth Flow**: Seamless provider authentication
- **Account Removal**: Integrated delete with data cleanup

#### Settings Integration Points
- `/user/integrations` - Main integration management
- Sidebar "Manage Integrations" button
- "Add Account" modal integration
- Real-time account status in settings

### Performance Optimizations

#### Virtual Scrolling
- **Large Lists**: Handles 10,000+ emails efficiently
- **Memory Management**: Only renders visible items
- **Smooth Scrolling**: 60fps scroll performance
- **Dynamic Heights**: Adaptive item sizing

#### Caching Strategy
- **Email Content**: Intelligent content caching
- **Attachment Preview**: Progressive loading
- **Search Results**: Debounced search with caching
- **Account Data**: Persistent local caching

#### Network Optimization
- **Batch Operations**: Grouped API calls
- **Compression**: Gzipped responses
- **CDN Integration**: Attachment delivery optimization
- **Connection Pooling**: Efficient provider connections

## API Endpoints

### Email Validation

**POST** `/api/integrations/email/validate`

Validates email provider credentials and connection settings.

**Request Body**:
```typescript
interface ValidateEmailRequest {
  provider: EmailProvider
  credentials: EmailCredentials
  testConnection?: boolean
}
```

**Response**:
```typescript
interface ValidateEmailResponse {
  valid: boolean
  error?: string
  serverCapabilities?: string[]
  supportedFolders?: string[]
}
```

### Email Synchronization

**POST** `/api/integrations/email/sync`

Synchronizes emails from the specified provider and account.

**Request Body**:
```typescript
interface SyncEmailRequest {
  accountId: string
  folders?: string[]
  since?: string // ISO date string
  limit?: number
}
```

**Response**:
```typescript
interface SyncEmailResponse {
  success: boolean
  emailCount: number
  errors?: string[]
  lastSyncAt: string
}
```

### Send Email

**POST** `/api/integrations/email/send`

Sends an email using the specified email account.

**Request Body**:
```typescript
interface SendEmailRequest {
  accountId: string
  to: string[]
  cc?: string[]
  bcc?: string[]
  subject: string
  content: {
    text: string
    html?: string
  }
  attachments?: EmailAttachment[]
  inReplyTo?: string
  replyTo?: string
}
```

**Response**:
```typescript
interface SendEmailResponse {
  success: boolean
  messageId?: string
  error?: string
}
```

## Security & Authentication

### Credential Encryption

All email credentials are encrypted using AES-256 encryption before storage:

```typescript
interface EmailCredentials {
  // OAuth credentials (encrypted)
  accessToken?: string
  refreshToken?: string
  expiresAt?: Timestamp

  // Password credentials (encrypted)
  email?: string
  password?: string
  appPassword?: string

  // Server configuration
  host?: string
  port?: number
  secure?: boolean
}
```

### Firebase Security Rules

```javascript
// Email accounts collection
match /email_accounts/{accountId} {
  allow read, write: if request.auth != null
    && request.auth.uid == resource.data.userId
    && validateEmailAccount(request.resource.data);
}

// Email messages collection
match /email_messages/{messageId} {
  allow read, write: if request.auth != null
    && request.auth.uid == resource.data.userId
    && exists(/databases/$(database)/documents/email_accounts/$(resource.data.accountId));
}
```

### OAuth Security

OAuth flows follow security best practices:
- PKCE (Proof Key for Code Exchange) for public clients
- State parameter for CSRF protection
- Secure token storage with automatic refresh
- Minimum required scopes requested

## Setup & Configuration

### Environment Variables

```bash
# Gmail OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key
```

### Firebase Configuration

1. **Enable Authentication** with Google and Microsoft providers
2. **Configure Firestore** with security rules
3. **Set up Cloud Functions** for email processing
4. **Configure Storage** for email attachments

### Development Setup

```bash
# Install dependencies
pnpm install

# Start Firebase emulators
pnpm emulators

# Start development server
pnpm dev
```

## Usage Guide

### Adding an Email Account

1. Navigate to **Settings > Integrations**
2. Click **Add Integration** in the Email section
3. Select your email provider
4. Follow the provider-specific setup flow:
   - **OAuth providers**: Complete OAuth authorization
   - **Password providers**: Enter credentials and server settings
5. Test the connection and save

### Managing Emails

1. **Viewing Emails**: Navigate to the inbox to see all synchronized emails
2. **Switching Accounts**: Use the account selector to switch between email accounts
3. **Filtering**: Use the search bar and filters to find specific emails
4. **Replying**: Click reply button to compose a response
5. **Organizing**: Use folders and labels to organize emails

### Composing Emails

1. Click the **Compose** button
2. Select the sending account
3. Enter recipients, subject, and content
4. Add attachments if needed
5. Send or save as draft

## Testing Strategy

### Unit Tests

```typescript
// Component testing
describe('TairoEmailList', () => {
  it('should display emails correctly', () => {
    // Test implementation
  })

  it('should handle search filtering', () => {
    // Test implementation
  })
})

// API testing
describe('Email API', () => {
  it('should validate email credentials', () => {
    // Test implementation
  })
})
```

### Integration Tests

```typescript
// Email provider integration
describe('Gmail Provider', () => {
  it('should authenticate with OAuth', () => {
    // Test implementation
  })

  it('should sync emails successfully', () => {
    // Test implementation
  })
})
```

### End-to-End Tests

```typescript
// Full email workflow
describe('Email Workflow', () => {
  it('should complete email setup and sync', () => {
    // Test implementation using Playwright
  })
})
```

## Troubleshooting

### Common Issues

#### OAuth Authentication Failures

**Problem**: OAuth flow fails with "invalid_client" error
**Solution**:
1. Verify client ID and secret in environment variables
2. Check OAuth redirect URLs in provider console
3. Ensure proper scopes are requested

#### Email Sync Issues

**Problem**: Emails not synchronizing
**Solution**:
1. Check network connectivity
2. Verify email account credentials
3. Review Firebase security rules
4. Check API rate limits

#### Performance Issues

**Problem**: Slow email loading
**Solution**:
1. Implement pagination for large email lists
2. Use email caching strategies
3. Optimize database queries
4. Consider background sync jobs

### Debug Mode

Enable debug logging:

```typescript
// In composables/useEmailAccounts.ts
const DEBUG_EMAIL = process.env.NODE_ENV === 'development'

if (DEBUG_EMAIL) {
  console.log('Email operation:', operation, data)
}
```

### Support Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Gmail API Documentation](https://developers.google.com/gmail/api)
- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/api/resources/mail-api-overview)
- [Project GitHub Issues](https://github.com/your-org/pib/issues)

## Contributing

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Implement changes following LEVER principles
4. Add comprehensive tests
5. Update documentation
6. Submit pull request

### Code Standards

- Follow existing TypeScript patterns
- Use Tairo component conventions
- Implement proper error handling
- Add JSDoc comments for public APIs
- Follow security best practices

---

*This documentation is maintained by the BMAD development team and follows the comprehensive email system implementation completed through agent coordination.*
