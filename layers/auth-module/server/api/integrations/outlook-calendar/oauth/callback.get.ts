import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { OAuthService } from '../../../../services/oauth-base'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Outlook Calendar OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Verify state
    const storedState = getCookie(event, 'outlook-calendar_oauth_state')
    if (!storedState || storedState !== state) {
      throw new Error('Invalid OAuth state')
    }

    // Clear state cookie
    deleteCookie(event, 'outlook-calendar_oauth_state')

    // Parse state to get user info
    const stateData = JSON.parse(state as string)
    const { userId, workspaceId, returnUrl } = stateData

    if (!userId || !workspaceId) {
      throw new Error('Invalid state data')
    }

    // Exchange code for access token
    const redirectUri = `${getRequestURL(event).origin}/api/integrations/outlook-calendar/oauth/callback`
    const tokenData = await OAuthService.exchangeCodeForToken('outlook', code as string, redirectUri)

    // Get user profile from Microsoft Graph
    const profile = await OAuthService.getUserProfile('outlook', tokenData.accessToken)

    // Get user's calendars
    let calendars = []
    try {
      const calendarsResponse = await fetch(
        'https://graph.microsoft.com/v1.0/me/calendars',
        {
          headers: {
            Authorization: `Bearer ${tokenData.accessToken}`,
            Accept: 'application/json',
          },
        },
      )

      if (calendarsResponse.ok) {
        const calendarsData = await calendarsResponse.json()
        calendars = calendarsData.value || []
      }
    }
    catch (error) {
      console.error('Failed to fetch Outlook calendars:', error)
    }

    const primaryCalendar = calendars.find(cal => cal.isDefaultCalendar) || calendars[0]

    if (!primaryCalendar) {
      throw new Error('No calendar found')
    }

    // Create calendar integration document
    const integration = {
      userId,
      workspaceId,
      provider: 'outlook',
      credentials: {
        accessToken: tokenData.accessToken,
        refreshToken: tokenData.refreshToken,
        expiryDate: tokenData.expiresIn ? Date.now() + (tokenData.expiresIn * 1000) : Date.now() + 3600000,
        tokenType: tokenData.tokenType || 'Bearer',
      },
      calendarId: primaryCalendar.id,
      calendarName: primaryCalendar.name || 'Primary Calendar',
      isActive: true,
      syncStatus: {
        lastSync: new Date(),
        isActive: true,
        syncDirection: 'bidirectional',
        eventCount: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Save to Firestore
    await db.collection('calendar_integrations').add(integration)

    // Redirect back to the original page
    const redirectUrl = returnUrl || `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations`

    return sendRedirect(event, `${redirectUrl}?connected=outlook-calendar`)
  }
  catch (error) {
    console.error('Outlook Calendar OAuth callback error:', error)

    const errorUrl = `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations?error=${encodeURIComponent(error.message)}`
    return sendRedirect(event, errorUrl)
  }
})
