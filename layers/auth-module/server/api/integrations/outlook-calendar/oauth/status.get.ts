export default defineEventHandler(async (event) => {
  // Check if O<PERSON>uth was successful by looking for the success cookie
  const successData = getCookie(event, 'outlook-calendar_oauth_success')
  const errorData = getCookie(event, 'outlook-calendar_oauth_error')

  if (errorData) {
    // Clear the error cookie
    deleteCookie(event, 'outlook-calendar_oauth_error')
    
    return {
      authenticated: false,
      error: errorData,
    }
  }

  if (!successData) {
    return {
      authenticated: false,
    }
  }

  try {
    const data = JSON.parse(successData)

    // Clear the cookie after reading
    deleteCookie(event, 'outlook-calendar_oauth_success')

    return {
      authenticated: true,
      profile: data.profile,
      calendars: data.calendars,
      tokenData: data.tokenData,
      features: ['calendar', 'events', 'sync'],
    }
  }
  catch (error) {
    console.error('Failed to parse Outlook Calendar OAuth success data:', error)
    return {
      authenticated: false,
      error: 'Failed to parse authentication data',
    }
  }
})
