import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { workspaceId, userId } = query

    if (!workspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required',
      })
    }

    // Build query
    let firestoreQuery = db.collection('calendar_integrations')
      .where('workspaceId', '==', workspaceId)

    if (userId) {
      firestoreQuery = firestoreQuery.where('userId', '==', userId)
    }

    const snapshot = await firestoreQuery.get()

    const integrations = snapshot.docs.map((doc) => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        // Don't expose sensitive credentials
        credentials: {
          hasToken: !!data.credentials?.accessToken,
          expiryDate: data.credentials?.expiryDate,
        },
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        syncStatus: {
          ...data.syncStatus,
          lastSync: data.syncStatus?.lastSync?.toDate(),
          nextSync: data.syncStatus?.nextSync?.toDate(),
        },
      }
    })

    return {
      success: true,
      integrations,
    }
  }
  catch (error) {
    console.error('Calendar integrations fetch error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendar integrations: ${error.message}`,
    })
  }
})
