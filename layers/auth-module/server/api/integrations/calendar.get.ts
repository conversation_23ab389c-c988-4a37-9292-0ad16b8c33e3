import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}

// Get Firestore instance with error handling
let db: FirebaseFirestore.Firestore | null = null
try {
  db = getFirestore()
} catch (error) {
  console.warn('Failed to initialize Firestore:', error)
}

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { workspaceId, userId } = query

    if (!workspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required',
      })
    }

    // Check if Firestore is available
    if (!db) {
      console.warn('Firestore not available, returning empty integrations list')
      return {
        success: true,
        integrations: [],
      }
    }

    // Build query with error handling
    let firestoreQuery = db.collection('calendar_integrations')
      .where('workspaceId', '==', workspaceId)

    if (userId) {
      firestoreQuery = firestoreQuery.where('userId', '==', userId)
    }

    let snapshot
    try {
      snapshot = await firestoreQuery.get()
    } catch (firestoreError) {
      console.error('Firestore query failed:', firestoreError)
      // Return empty list if Firestore query fails (e.g., emulator issues)
      return {
        success: true,
        integrations: [],
      }
    }

    const integrations = snapshot.docs.map((doc) => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        // Don't expose sensitive credentials
        credentials: {
          hasToken: !!data.credentials?.accessToken,
          expiryDate: data.credentials?.expiryDate,
        },
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        syncStatus: {
          ...data.syncStatus,
          lastSync: data.syncStatus?.lastSync?.toDate(),
          nextSync: data.syncStatus?.nextSync?.toDate(),
        },
      }
    })

    return {
      success: true,
      integrations,
    }
  }
  catch (error) {
    console.error('Calendar integrations fetch error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to fetch calendar integrations: ${error.message}`,
    })
  }
})
