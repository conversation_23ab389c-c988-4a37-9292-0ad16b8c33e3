import { OAuthService } from '../../../../services/oauth-base'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Outlook OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Verify state
    const storedState = getCookie(event, 'outlook_oauth_state')
    if (!storedState || storedState !== state) {
      throw new Error('Invalid OAuth state')
    }

    // Clear state cookie
    deleteCookie(event, 'outlook_oauth_state')

    // Exchange code for access token
    const redirectUri = `${getRequestURL(event).origin}/api/integrations/outlook/oauth/callback`
    const tokenData = await OAuthService.exchangeCodeForToken('outlook', code as string, redirectUri)

    // Get user profile from Microsoft Graph
    const profile = await OAuthService.getUserProfile('outlook', tokenData.accessToken)

    // Get additional Outlook/Exchange info
    let outlookProfile = {}
    try {
      const outlookResponse = await fetch(
        'https://graph.microsoft.com/v1.0/me/mailboxSettings',
        {
          headers: {
            Authorization: `Bearer ${tokenData.accessToken}`,
            Accept: 'application/json',
          },
        },
      )

      if (outlookResponse.ok) {
        outlookProfile = await outlookResponse.json()
      }
    }
    catch (error) {
      console.error('Failed to fetch Outlook mailbox settings:', error)
    }

    // Store success data in cookie for status endpoint
    const successData = {
      profile: {
        ...profile,
        mailboxSettings: outlookProfile,
      },
      tokenData,
    }

    setCookie(event, 'outlook_oauth_success', JSON.stringify(successData), {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that closes the popup and notifies parent
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Outlook Connected</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-success',
                provider: 'outlook'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?connected=outlook';
            }
          </script>
          <p>Outlook connected successfully. You can close this window.</p>
        </body>
      </html>
    `
  }
  catch (error: any) {
    console.error('Outlook OAuth callback error:', error)

    // Store error in cookie for status endpoint
    setCookie(event, 'outlook_oauth_error', error.message, {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that notifies parent of error
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Outlook Connection Failed</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-error',
                provider: 'outlook',
                error: '${error.message}'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?error=${encodeURIComponent(error.message)}';
            }
          </script>
          <p>Failed to connect Outlook: ${error.message}</p>
        </body>
      </html>
    `
  }
})
