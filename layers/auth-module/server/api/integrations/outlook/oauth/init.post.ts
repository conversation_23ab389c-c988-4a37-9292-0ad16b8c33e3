import { OAuthService } from '../../../../services/oauth-base'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { redirectUri } = body

  try {
    // Generate state for OAuth flow
    const state = OAuthService.generateState()

    // Store state in cookie for verification
    OAuthService.storeState(event, 'outlook', state)

    // Build OAuth authorization URL with Microsoft-specific parameters
    const authUrl = OAuthService.buildAuthUrl('outlook', redirectUri, state, {
      response_mode: 'query',
      prompt: 'consent', // Force consent screen to get refresh token
    })

    return {
      authUrl,
      state,
    }
  }
  catch (error: any) {
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to initialize Outlook OAuth',
    })
  }
})
