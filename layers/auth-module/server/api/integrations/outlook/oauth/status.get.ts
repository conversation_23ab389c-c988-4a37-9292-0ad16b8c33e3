export default defineEventHandler(async (event) => {
  // Check if OAuth was successful by looking for the success cookie
  const successData = getCookie(event, 'outlook_oauth_success')
  const errorData = getCookie(event, 'outlook_oauth_error')

  if (errorData) {
    // Clear the error cookie
    deleteCookie(event, 'outlook_oauth_error')
    
    return {
      authenticated: false,
      error: errorData,
    }
  }

  if (!successData) {
    return {
      authenticated: false,
    }
  }

  try {
    const data = JSON.parse(successData)

    // Clear the cookie after reading
    deleteCookie(event, 'outlook_oauth_success')

    return {
      authenticated: true,
      profile: data.profile,
      tokenData: data.tokenData,
      features: ['read', 'send', 'calendar', 'contacts'],
    }
  }
  catch (error) {
    console.error('Failed to parse Outlook OAuth success data:', error)
    return {
      authenticated: false,
      error: 'Failed to parse authentication data',
    }
  }
})
