import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { google } from 'googleapis'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { integrationId } = body

    if (!integrationId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID is required',
      })
    }

    // Get integration document
    const integrationDoc = await db.collection('calendar_integrations').doc(integrationId).get()

    if (!integrationDoc.exists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integration = integrationDoc.data()

    // Revoke Google tokens
    try {
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
      )

      oauth2Client.setCredentials({
        access_token: integration.credentials.accessToken,
        refresh_token: integration.credentials.refreshToken,
      })

      await oauth2Client.revokeCredentials()
    }
    catch (revokeError) {
      console.warn('Failed to revoke Google tokens:', revokeError)
      // Continue with local cleanup even if revocation fails
    }

    // Delete integration document
    await integrationDoc.ref.delete()

    return {
      success: true,
      message: 'Google Calendar integration disconnected successfully',
    }
  }
  catch (error) {
    console.error('Google Calendar disconnect error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to disconnect Google Calendar: ${error.message}`,
    })
  }
})
