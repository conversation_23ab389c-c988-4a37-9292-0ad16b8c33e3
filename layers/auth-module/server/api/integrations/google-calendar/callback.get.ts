import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { google } from 'googleapis'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Google OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Parse state
    const stateData = JSON.parse(state as string)
    const { userId, workspaceId, returnUrl } = stateData

    if (!userId || !workspaceId) {
      throw new Error('Invalid state data')
    }

    // Exchange code for tokens
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NUXT_PUBLIC_BASE_URL}/api/integrations/google-calendar/callback`,
    )

    const { tokens } = await oauth2Client.getToken(code as string)

    if (!tokens.access_token || !tokens.refresh_token) {
      throw new Error('Failed to obtain valid tokens')
    }

    // Set credentials for API calls
    oauth2Client.setCredentials(tokens)

    // Get user's calendar list
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })
    const calendarsResponse = await calendar.calendarList.list()

    const primaryCalendar = calendarsResponse.data.items?.find(cal => cal.primary)

    if (!primaryCalendar) {
      throw new Error('No primary calendar found')
    }

    // Create calendar integration document
    const integration = {
      userId,
      workspaceId,
      provider: 'google',
      credentials: {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiryDate: tokens.expiry_date || Date.now() + 3600000,
        tokenType: tokens.token_type || 'Bearer',
      },
      calendarId: primaryCalendar.id,
      calendarName: primaryCalendar.summary || 'Primary Calendar',
      isActive: true,
      syncStatus: {
        lastSync: new Date(),
        isActive: true,
        syncDirection: 'bidirectional',
        eventCount: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Save to Firestore
    await db.collection('calendar_integrations').add(integration)

    // Redirect back to the original page
    const redirectUrl = returnUrl || `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations`

    return sendRedirect(event, `${redirectUrl}?connected=google-calendar`)
  }
  catch (error) {
    console.error('Google Calendar OAuth callback error:', error)

    const errorUrl = `${process.env.NUXT_PUBLIC_BASE_URL}/user/integrations?error=${encodeURIComponent(error.message)}`
    return sendRedirect(event, errorUrl)
  }
})
