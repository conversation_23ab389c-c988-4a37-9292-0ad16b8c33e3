import { OAuthService } from '../../../services/oauth-base'
import { google } from 'googleapis'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Google Calendar OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Verify state to prevent CSRF attacks
    const isValidState = OAuthService.verifyState(event, 'google-calendar', state as string)
    if (!isValidState) {
      throw new Error('Invalid OAuth state')
    }

    // Exchange code for access token
    const redirectUri = `${getRequestURL(event).origin}/api/integrations/google-calendar/callback`
    const tokenData = await OAuthService.exchangeCodeForToken('google-calendar', code as string, redirectUri)

    // Get user profile
    const profile = await OAuthService.getUserProfile('google-calendar', tokenData.accessToken)

    // Get user's calendar list using Google APIs
    const oauth2Client = new google.auth.OAuth2()
    oauth2Client.setCredentials({ access_token: tokenData.accessToken })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })
    const calendarsResponse = await calendar.calendarList.list()

    const primaryCalendar = calendarsResponse.data.items?.find(cal => cal.primary)

    if (!primaryCalendar) {
      throw new Error('No primary calendar found')
    }

    // Store the integration data in a temporary session cookie
    // This follows the same pattern as Gmail and YouTube integrations
    const integrationData = {
      profile,
      calendar: {
        id: primaryCalendar.id,
        name: primaryCalendar.summary || 'Primary Calendar',
        primary: true,
      },
      calendars: calendarsResponse.data.items || [],
      tokenData: {
        ...tokenData,
        expiresAt: tokenData.expiresIn ? new Date(Date.now() + tokenData.expiresIn * 1000).toISOString() : null,
      },
    }

    setCookie(event, 'google_calendar_oauth_success', JSON.stringify(integrationData), {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that closes the popup and notifies parent
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Google Calendar Connected</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-success',
                provider: 'google-calendar'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?connected=google-calendar';
            }
          </script>
          <p>Google Calendar connected successfully. You can close this window.</p>
        </body>
      </html>
    `
  }
  catch (error: any) {
    console.error('Google Calendar OAuth callback error:', error)

    // Store error in cookie for status endpoint
    setCookie(event, 'google_calendar_oauth_error', error.message, {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that notifies parent of error
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Google Calendar Connection Failed</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-error',
                provider: 'google-calendar',
                error: '${error.message || 'Connection failed'}'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?error=${encodeURIComponent(error.message)}';
            }
          </script>
          <p>Failed to connect Google Calendar: ${error.message || 'An error occurred during authentication'}</p>
        </body>
      </html>
    `
  }
})
