import { google } from 'googleapis'

export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Google OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Parse state
    const stateData = JSON.parse(state as string)
    const { userId, workspaceId, returnUrl } = stateData

    if (!userId || !workspaceId) {
      throw new Error('Invalid state data')
    }

    // Exchange code for tokens
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${baseUrl}/api/integrations/google-calendar/callback`,
    )

    const { tokens } = await oauth2Client.getToken(code as string)

    if (!tokens.access_token || !tokens.refresh_token) {
      throw new Error('Failed to obtain valid tokens')
    }

    // Set credentials for API calls
    oauth2Client.setCredentials(tokens)

    // Get user's calendar list
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })
    const calendarsResponse = await calendar.calendarList.list()

    const primaryCalendar = calendarsResponse.data.items?.find(cal => cal.primary)

    if (!primaryCalendar) {
      throw new Error('No primary calendar found')
    }

    // Store the integration data in a temporary session cookie
    // This follows the same pattern as Gmail and YouTube integrations
    const integrationData = {
      profile: {
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
      },
      calendar: {
        id: primaryCalendar.id,
        name: primaryCalendar.summary || 'Primary Calendar',
        primary: true,
      },
      calendars: calendarsResponse.data.items || [],
      tokenData: {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiresAt: tokens.expiry_date ? new Date(tokens.expiry_date).toISOString() : null,
        tokenType: tokens.token_type || 'Bearer',
      },
      userId,
      workspaceId,
    }

    setCookie(event, 'google_calendar_oauth_success', JSON.stringify(integrationData), {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that closes the popup and notifies parent
    return `
      <html>
        <head>
          <title>Google Calendar Connection Successful</title>
          <script>
            window.opener?.postMessage({
              type: 'oauth-success',
              provider: 'google-calendar'
            }, '*');
            setTimeout(() => window.close(), 1000);
          </script>
        </head>
        <body>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2>Google Calendar Connected Successfully!</h2>
            <p>You can close this window.</p>
          </div>
        </body>
      </html>
    `
  }
  catch (error) {
    console.error('Google Calendar OAuth callback error:', error)

    // Store error in cookie for status endpoint
    setCookie(event, 'google_calendar_oauth_error', error.message, {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that notifies parent of error
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Google Calendar Connection Failed</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-error',
                provider: 'google-calendar',
                error: '${error.message || 'Connection failed'}'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?error=${encodeURIComponent(error.message)}';
            }
          </script>
          <p>Failed to connect Google Calendar: ${error.message || 'An error occurred during authentication'}</p>
        </body>
      </html>
    `
  }
})
