import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { google } from 'googleapis'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    const baseUrl = config.public.oauth?.google?.baseUrl || 'http://localhost:3000'
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Google OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Parse state
    const stateData = JSON.parse(state as string)
    const { userId, workspaceId, returnUrl } = stateData

    if (!userId || !workspaceId) {
      throw new Error('Invalid state data')
    }

    // Exchange code for tokens
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${baseUrl}/api/integrations/google-calendar/callback`,
    )

    const { tokens } = await oauth2Client.getToken(code as string)

    if (!tokens.access_token || !tokens.refresh_token) {
      throw new Error('Failed to obtain valid tokens')
    }

    // Set credentials for API calls
    oauth2Client.setCredentials(tokens)

    // Get user's calendar list
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })
    const calendarsResponse = await calendar.calendarList.list()

    const primaryCalendar = calendarsResponse.data.items?.find(cal => cal.primary)

    if (!primaryCalendar) {
      throw new Error('No primary calendar found')
    }

    // Create calendar integration document
    const integration = {
      userId,
      workspaceId,
      provider: 'google',
      credentials: {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiryDate: tokens.expiry_date || Date.now() + 3600000,
        tokenType: tokens.token_type || 'Bearer',
      },
      calendarId: primaryCalendar.id,
      calendarName: primaryCalendar.summary || 'Primary Calendar',
      isActive: true,
      syncStatus: {
        lastSync: new Date(),
        isActive: true,
        syncDirection: 'bidirectional',
        eventCount: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Save to Firestore
    await db.collection('calendar_integrations').add(integration)

    // Return HTML that closes the popup and notifies parent
    return `
      <html>
        <head>
          <title>Google Calendar Connection Successful</title>
          <script>
            window.opener?.postMessage({
              type: 'oauth-success',
              provider: 'google-calendar'
            }, '*');
            setTimeout(() => window.close(), 1000);
          </script>
        </head>
        <body>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2>Google Calendar Connected Successfully!</h2>
            <p>You can close this window.</p>
          </div>
        </body>
      </html>
    `
  }
  catch (error) {
    console.error('Google Calendar OAuth callback error:', error)

    // Return error page that notifies parent
    return `
      <html>
        <head>
          <title>Google Calendar Connection Failed</title>
          <script>
            window.opener?.postMessage({
              type: 'oauth-error',
              provider: 'google-calendar',
              error: '${error.message || 'Connection failed'}'
            }, '*');
            setTimeout(() => window.close(), 3000);
          </script>
        </head>
        <body>
          <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
            <h2 style="color: red;">Google Calendar Connection Failed</h2>
            <p>${error.message || 'An error occurred during authentication'}</p>
            <p>This window will close automatically.</p>
          </div>
        </body>
      </html>
    `
  }
})
