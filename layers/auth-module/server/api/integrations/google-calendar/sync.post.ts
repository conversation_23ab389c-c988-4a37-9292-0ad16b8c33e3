import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { google } from 'googleapis'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

// Convert Google Calendar event to our format
function googleEventToCalendarEvent(googleEvent: any, userId: string, workspaceId: string) {
  const startDate = new Date(googleEvent.start?.dateTime || googleEvent.start?.date)
  const endDate = new Date(googleEvent.end?.dateTime || googleEvent.end?.date)

  return {
    title: googleEvent.summary || 'Untitled Event',
    description: googleEvent.description || '',
    location: googleEvent.location || '',
    startDate: Timestamp.fromDate(startDate),
    endDate: Timestamp.fromDate(endDate),
    duration: Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60)), // duration in minutes
    category: 'personal',
    status: 'confirmed',
    userId,
    workspaceId,
    participants: googleEvent.attendees?.map((attendee: any) => ({
      id: Math.random(), // Generate random ID for now
      name: attendee.displayName || attendee.email,
      photo: '/img/avatars/default.svg',
    })) || [],
    googleEventId: googleEvent.id,
    syncedAt: Timestamp.now(),
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  }
}

// Convert our calendar event to Google format
function calendarEventToGoogle(event: any) {
  return {
    summary: event.title,
    description: event.description || '',
    location: event.location || '',
    start: {
      dateTime: event.startDate.toDate().toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    end: {
      dateTime: event.endDate.toDate().toISOString(),
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    attendees: event.participants?.map((participant: any) => ({
      email: participant.email || `${participant.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
      displayName: participant.name,
    })) || [],
  }
}

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { integrationId, direction, eventIds } = body

    if (!integrationId || !direction) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Integration ID and direction are required',
      })
    }

    if (!['import', 'export', 'bidirectional'].includes(direction)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid sync direction',
      })
    }

    // Get integration document
    const integrationDoc = await db.collection('calendar_integrations').doc(integrationId).get()

    if (!integrationDoc.exists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Integration not found',
      })
    }

    const integration = integrationDoc.data()

    // Setup Google Calendar API
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
    )

    oauth2Client.setCredentials({
      access_token: integration.credentials.accessToken,
      refresh_token: integration.credentials.refreshToken,
    })

    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    let importedCount = 0
    let exportedCount = 0

    // Import from Google Calendar
    if (direction === 'import' || direction === 'bidirectional') {
      const now = new Date()
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

      const eventsResponse = await calendar.events.list({
        calendarId: integration.calendarId,
        timeMin: oneMonthAgo.toISOString(),
        timeMax: oneMonthFromNow.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
      })

      const googleEvents = eventsResponse.data.items || []

      for (const googleEvent of googleEvents) {
        if (!googleEvent.id)
          continue

        // Check if event already exists
        const existingEvent = await db.collection('calendar_events')
          .where('googleEventId', '==', googleEvent.id)
          .where('workspaceId', '==', integration.workspaceId)
          .get()

        if (existingEvent.empty) {
          // Create new event
          const calendarEvent = googleEventToCalendarEvent(
            googleEvent,
            integration.userId,
            integration.workspaceId,
          )

          await db.collection('calendar_events').add(calendarEvent)
          importedCount++
        }
      }
    }

    // Export to Google Calendar
    if (direction === 'export' || direction === 'bidirectional') {
      const query = db.collection('calendar_events')
        .where('workspaceId', '==', integration.workspaceId)
        .where('googleEventId', '==', null) // Only events not yet synced to Google

      if (eventIds && eventIds.length > 0) {
        // Export specific events
        for (const eventId of eventIds) {
          const eventDoc = await db.collection('calendar_events').doc(eventId).get()
          if (eventDoc.exists) {
            const eventData = eventDoc.data()

            if (!eventData.googleEventId) {
              const googleEvent = calendarEventToGoogle(eventData)

              const createdEvent = await calendar.events.insert({
                calendarId: integration.calendarId,
                requestBody: googleEvent,
              })

              // Update local event with Google ID
              await eventDoc.ref.update({
                googleEventId: createdEvent.data.id,
                syncedAt: Timestamp.now(),
                updatedAt: Timestamp.now(),
              })

              exportedCount++
            }
          }
        }
      }
      else {
        // Export all unsynced events
        const unsyncedEvents = await query.get()

        for (const eventDoc of unsyncedEvents.docs) {
          const eventData = eventDoc.data()
          const googleEvent = calendarEventToGoogle(eventData)

          const createdEvent = await calendar.events.insert({
            calendarId: integration.calendarId,
            requestBody: googleEvent,
          })

          // Update local event with Google ID
          await eventDoc.ref.update({
            googleEventId: createdEvent.data.id,
            syncedAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
          })

          exportedCount++
        }
      }
    }

    // Update sync status
    await integrationDoc.ref.update({
      'syncStatus.lastSync': Timestamp.now(),
      'syncStatus.eventCount': importedCount + exportedCount,
      'updatedAt': Timestamp.now(),
    })

    return {
      success: true,
      importedCount,
      exportedCount,
      eventCount: importedCount + exportedCount,
      message: `Sync completed: ${importedCount} imported, ${exportedCount} exported`,
    }
  }
  catch (error) {
    console.error('Google Calendar sync error:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: `Failed to sync Google Calendar: ${error.message}`,
    })
  }
})
