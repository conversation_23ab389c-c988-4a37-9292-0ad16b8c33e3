export default defineEventHandler(async (event) => {
  // Check if <PERSON><PERSON><PERSON> was successful by looking for the success cookie
  const successData = getC<PERSON>ie(event, 'google_calendar_oauth_success')
  const errorData = getCookie(event, 'google_calendar_oauth_error')

  if (errorData) {
    // Clear the error cookie
    deleteCookie(event, 'google_calendar_oauth_error')
    
    return {
      authenticated: false,
      error: errorData,
    }
  }

  if (!successData) {
    return {
      authenticated: false,
    }
  }

  try {
    const data = JSON.parse(successData)

    // Clear the cookie after reading
    deleteCookie(event, 'google_calendar_oauth_success')

    return {
      authenticated: true,
      profile: data.profile,
      calendar: data.calendar,
      calendars: data.calendars,
      tokenData: data.tokenData,
      userId: data.userId,
      workspaceId: data.workspaceId,
      features: ['read', 'write', 'events', 'calendars'],
    }
  }
  catch (error) {
    console.error('Failed to parse Google Calendar OAuth success data:', error)
    
    // Clear the corrupted cookie
    deleteCookie(event, 'google_calendar_oauth_success')
    
    return {
      authenticated: false,
      error: 'Failed to parse authentication data',
    }
  }
})
