export default defineEventHandler(async (event) => {
  // Check if O<PERSON>uth was successful by looking for the success cookie
  const successData = getCookie(event, 'gmail_oauth_success')
  const errorData = getCookie(event, 'gmail_oauth_error')

  if (errorData) {
    // Clear the error cookie
    deleteCookie(event, 'gmail_oauth_error')
    
    return {
      authenticated: false,
      error: errorData,
    }
  }

  if (!successData) {
    return {
      authenticated: false,
    }
  }

  try {
    const data = JSON.parse(successData)

    // Clear the cookie after reading
    deleteCookie(event, 'gmail_oauth_success')

    return {
      authenticated: true,
      profile: data.profile,
      tokenData: data.tokenData,
      features: ['read', 'send', 'labels', 'threads', 'attachments'],
    }
  }
  catch (error) {
    console.error('Failed to parse Gmail OAuth success data:', error)
    return {
      authenticated: false,
      error: 'Failed to parse authentication data',
    }
  }
})
