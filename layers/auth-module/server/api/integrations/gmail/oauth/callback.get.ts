import { OAuthService } from '../../../../services/oauth-base'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { code, state, error: authError } = query

    if (authError) {
      throw new Error(`Gmail OAuth error: ${authError}`)
    }

    if (!code || !state) {
      throw new Error('Missing authorization code or state')
    }

    // Verify state
    const storedState = getCookie(event, 'gmail_oauth_state')
    if (!storedState || storedState !== state) {
      throw new Error('Invalid OAuth state')
    }

    // Clear state cookie
    deleteCookie(event, 'gmail_oauth_state')

    // Exchange code for access token
    const redirectUri = `${getRequestURL(event).origin}/api/integrations/gmail/oauth/callback`
    const tokenData = await OAuthService.exchangeCodeForToken('gmail', code as string, redirectUri)

    // Get user profile
    const profile = await OAuthService.getUserProfile('gmail', tokenData.accessToken)

    // Get Gmail profile for additional info
    let gmailProfile = {}
    try {
      const gmailResponse = await fetch(
        'https://gmail.googleapis.com/gmail/v1/users/me/profile',
        {
          headers: {
            Authorization: `Bearer ${tokenData.accessToken}`,
            Accept: 'application/json',
          },
        },
      )

      if (gmailResponse.ok) {
        gmailProfile = await gmailResponse.json()
      }
    }
    catch (error) {
      console.error('Failed to fetch Gmail profile:', error)
    }

    // Store success data in cookie for status endpoint
    const successData = {
      profile: {
        ...profile,
        ...gmailProfile,
      },
      tokenData,
    }

    setCookie(event, 'gmail_oauth_success', JSON.stringify(successData), {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that closes the popup and notifies parent
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Gmail Connected</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-success',
                provider: 'gmail'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?connected=gmail';
            }
          </script>
          <p>Gmail connected successfully. You can close this window.</p>
        </body>
      </html>
    `
  }
  catch (error: any) {
    console.error('Gmail OAuth callback error:', error)

    // Store error in cookie for status endpoint
    setCookie(event, 'gmail_oauth_error', error.message, {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 5, // 5 minutes
    })

    // Return HTML that notifies parent of error
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Gmail Connection Failed</title>
        </head>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth-error',
                provider: 'gmail',
                error: '${error.message}'
              }, '*');
              window.close();
            } else {
              window.location.href = '/user/integrations?error=${encodeURIComponent(error.message)}';
            }
          </script>
          <p>Failed to connect Gmail: ${error.message}</p>
        </body>
      </html>
    `
  }
})
