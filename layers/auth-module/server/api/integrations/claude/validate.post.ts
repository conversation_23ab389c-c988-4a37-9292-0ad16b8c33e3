/**
 * Validate Claude API key endpoint
 * Tests if the provided API key is valid by making a simple request to Claude API
 */

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { apiKey } = body

  if (!apiKey) {
    throw createError({
      statusCode: 400,
      statusMessage: 'API key is required',
    })
  }

  // Basic format validation
  if (!apiKey.startsWith('sk-ant-api03-')) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid API key format. Claude API keys start with "sk-ant-api03-"',
    })
  }

  try {
    // Test the API key with a simple request to Claude API
    const response = await $fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'content-type': 'application/json',
      },
      body: {
        model: 'claude-3-5-haiku-latest',
        max_tokens: 1,
        messages: [
          { role: 'user', content: 'test' },
        ],
      },
    })

    return {
      valid: true,
      message: 'API key is valid',
      model: 'claude-3-5-haiku-latest',
      // Don't return the actual response for security
    }
  }
  catch (error: any) {
    console.error('Claude API validation error:', error)

    // Parse different types of API errors
    if (error.response?.status === 401) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid API key. Please check your Anthropic API key.',
      })
    }

    if (error.response?.status === 403) {
      throw createError({
        statusCode: 403,
        statusMessage: 'API key does not have sufficient permissions.',
      })
    }

    if (error.response?.status === 429) {
      throw createError({
        statusCode: 429,
        statusMessage: 'Rate limit exceeded. Please try again later.',
      })
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to validate API key. Please try again.',
    })
  }
})
