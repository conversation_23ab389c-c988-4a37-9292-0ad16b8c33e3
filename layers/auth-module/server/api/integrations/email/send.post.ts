import type { EmailTransporterConfig, OutgoingEmailMessage } from '../../../utils/email-transporter'
import { createError, defineEventHandler, readBody } from 'h3'
import { createTransporter, getProviderConfig, sendEmail } from '../../../utils/email-transporter'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const {
      accountId,
      provider,
      email,
      password,
      appPassword,
      smtpHost,
      smtpPort,
      secure,
      message,
    } = body

    if (!accountId || !provider || !email || !message) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID, provider, email, and message are required',
      })
    }

    if (!password && !appPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Password or app password is required',
      })
    }

    // Validate message structure
    if (!message.to || !message.subject) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Message must have recipients (to) and subject',
      })
    }

    if (!message.text && !message.html) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Message must have either text or HTML content',
      })
    }

    // Get provider-specific SMTP config
    const providerConfig = getProviderConfig(provider)

    // Build SMTP configuration
    const smtpConfig: EmailTransporterConfig = {
      host: smtpHost || providerConfig.host || 'localhost',
      port: smtpPort || providerConfig.port || 587,
      secure: secure !== undefined ? secure : (providerConfig.secure ?? false),
      auth: {
        user: email,
        pass: password || appPassword,
      },
    }

    // Add TLS requirement for common providers
    if (['gmail', 'outlook', 'yahoo'].includes(provider.toLowerCase())) {
      smtpConfig.requireTLS = true
    }

    console.log('Sending email via SMTP:', { ...smtpConfig, auth: { user: smtpConfig.auth.user, pass: '[REDACTED]' } })

    try {
      // Create transporter
      const transporter = createTransporter(smtpConfig)

      // Prepare email message
      const emailMessage: OutgoingEmailMessage = {
        from: message.from || email,
        to: Array.isArray(message.to) ? message.to : [message.to],
        cc: message.cc ? (Array.isArray(message.cc) ? message.cc : [message.cc]) : undefined,
        bcc: message.bcc ? (Array.isArray(message.bcc) ? message.bcc : [message.bcc]) : undefined,
        subject: message.subject,
        text: message.text,
        html: message.html,
        attachments: message.attachments,
      }

      // Send email
      const result = await sendEmail(transporter, emailMessage)

      if (result.success) {
        console.log('Email sent successfully:', result.messageId)
        return {
          success: true,
          messageId: result.messageId,
          accountId,
        }
      }
      else {
        throw createError({
          statusCode: 400,
          statusMessage: `Failed to send email: ${result.error}`,
        })
      }
    }
    catch (smtpError) {
      console.error('SMTP error:', smtpError)
      throw createError({
        statusCode: 400,
        statusMessage: `Email delivery failed: ${smtpError.message}`,
      })
    }
  }
  catch (error) {
    if (error.statusCode) {
      throw error
    }

    console.error('Send email error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error while sending email',
    })
  }
})
