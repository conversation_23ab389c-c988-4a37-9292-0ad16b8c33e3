import type { IMAPConfig } from '../../../utils/imap-client'
import { createError, defineEventHandler, readBody } from 'h3'
import { createIMAPConnection, getIMAPConfig } from '../../../utils/imap-client'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const {
      accountId,
      provider,
      email,
      password,
      appPassword,
      imapHost,
      imapPort,
      secure = true,
      syncFolders = ['INBOX'],
      maxEmails = 100,
    } = body

    if (!accountId || !provider || !email) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Account ID, provider, and email are required',
      })
    }

    if (!password && !appPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Password or app password is required',
      })
    }

    // Get provider-specific IMAP config
    const providerConfig = getIMAPConfig(provider)

    // Build IMAP configuration
    const imapConfig: IMAPConfig = {
      host: imapHost || providerConfig.host || 'localhost',
      port: imapPort || providerConfig.port || 993,
      secure: secure !== undefined ? secure : (providerConfig.secure ?? true),
      auth: {
        user: email,
        password: password || appPassword,
      },
    }

    console.log('Starting email sync for account:', accountId)
    console.log('IMAP config:', { ...imapConfig, auth: { user: imapConfig.auth.user, password: '[REDACTED]' } })

    const syncResults = {
      accountId,
      syncedFolders: [],
      totalMessages: 0,
      newMessages: 0,
      errors: [],
    }

    try {
      // Create IMAP connection
      const imapClient = await createIMAPConnection(imapConfig)

      try {
        // Get available folders
        const folders = await imapClient.getFolders()
        console.log('Available folders:', folders.map(f => f.name))

        // Sync each requested folder
        for (const folderName of syncFolders) {
          try {
            console.log(`Syncing folder: ${folderName}`)

            // Select folder
            const folderInfo = await imapClient.selectFolder(folderName)
            console.log(`Folder ${folderName} info:`, folderInfo)

            // Search for messages (get recent ones first)
            const messageUIDs = await imapClient.searchMessages(['ALL'])
            const limitedUIDs = messageUIDs.slice(-maxEmails) // Get last N messages

            if (limitedUIDs.length > 0) {
              // Fetch message metadata and bodies
              const messages = await imapClient.fetchMessages(limitedUIDs, {
                bodies: true,
                headers: true,
                attachments: true,
              })

              console.log(`Fetched ${messages.length} messages from ${folderName}`)

              // TODO: Store messages in Firebase
              // For now, we'll just count them
              syncResults.syncedFolders.push({
                name: folderName,
                messageCount: messages.length,
                lastSync: new Date().toISOString(),
              })

              syncResults.totalMessages += messages.length
              syncResults.newMessages += messages.filter(m => !m.flags.includes('\\Seen')).length
            }
            else {
              syncResults.syncedFolders.push({
                name: folderName,
                messageCount: 0,
                lastSync: new Date().toISOString(),
              })
            }
          }
          catch (folderError) {
            console.error(`Error syncing folder ${folderName}:`, folderError)
            syncResults.errors.push({
              folder: folderName,
              error: folderError.message,
            })
          }
        }
      }
      finally {
        // Always disconnect
        await imapClient.disconnect()
      }

      console.log('Email sync completed:', syncResults)
      return {
        success: true,
        syncResults,
      }
    }
    catch (connectionError) {
      console.error('IMAP connection error:', connectionError)
      throw createError({
        statusCode: 400,
        statusMessage: `Failed to connect to email server: ${connectionError.message}`,
      })
    }
  }
  catch (error) {
    if (error.statusCode) {
      throw error
    }

    console.error('Email sync error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error during email sync',
    })
  }
})
