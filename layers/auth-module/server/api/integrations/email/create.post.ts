import { createError, define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3'
import { getApps, initializeApp } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { encrypt } from '../../../../utils/encryption'

// Initialize Firebase Admin if not already done
if (!getApps().length) {
  initializeApp()
}
const db = getFirestore()

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const {
      provider,
      providerName,
      email,
      name,
      authType,
      config
    } = body

    // Validate required fields
    if (!provider || !email || !name) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Provider, email, and name are required',
      })
    }

    // Get user ID from auth context (this would need proper auth middleware)
    // For now, we'll use a placeholder
    const userId = event.context.user?.uid || 'demo-user'
    const workspaceId = event.context.workspace?.id || 'demo-workspace'

    // Encrypt sensitive data
    const masterKey = process.env.ENCRYPTION_KEY || 'default-key-for-development'
    const encryptedConfig = encrypt(JSON.stringify({
      password: config.password,
      appPassword: config.appPassword,
      ...config
    }), masterKey)

    // Create integration document
    const integrationData = {
      provider,
      providerName: providerName || provider,
      email,
      name,
      authType,
      userId,
      workspaceId,
      isActive: true,
      encryptedConfig,
      supportedFeatures: getSupportedFeatures(provider),
      syncSettings: {
        frequency: config.syncFrequency || 15,
        folders: config.syncFolders || ['INBOX'],
        enableAutoSync: config.enableAutoSync !== false,
        maxEmailsPerSync: config.maxEmailsPerSync || 100,
        enableAttachments: config.enableAttachments !== false,
        attachmentSizeLimit: config.attachmentSizeLimit || 25,
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    // Save to Firestore
    const docRef = await db
      .collection('email_integrations')
      .add(integrationData)

    // Return success response with integration ID
    return {
      success: true,
      integrationId: docRef.id,
      integration: {
        ...integrationData,
        id: docRef.id,
        // Don't return encrypted config in response
        encryptedConfig: undefined,
      }
    }
  }
  catch (error) {
    if (error.statusCode) {
      throw error
    }

    console.error('Email integration creation error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create email integration',
    })
  }
})

function getSupportedFeatures(provider: string): string[] {
  const featureMap = {
    gmail: ['oauth', 'imap', 'smtp', 'labels', 'threads'],
    outlook: ['oauth', 'exchange', 'folders', 'calendar'],
    yahoo: ['imap', 'smtp', 'app-password', 'folders'],
    custom: ['imap', 'smtp', 'custom-config'],
    imap: ['imap', 'smtp', 'custom-config'],
  }
  
  return featureMap[provider.toLowerCase()] || ['imap', 'smtp']
}