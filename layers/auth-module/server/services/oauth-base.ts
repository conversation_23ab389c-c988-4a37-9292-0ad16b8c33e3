import type { H3Event } from 'h3'
import { getSocialProvider } from '../../config/social-providers'

export class OAuthService {
  /**
   * Generate a random state string for OAuth security
   */
  static generateState(length = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * Store OAuth state in cookie for verification
   */
  static storeState(event: H3Event, provider: string, state: string): void {
    setCookie(event, `${provider}_oauth_state`, state, {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      maxAge: 60 * 10, // 10 minutes
    })
  }

  /**
   * Verify OAuth state from cookie
   */
  static verifyState(event: H3Event, provider: string, state: string): boolean {
    const storedState = getCookie(event, `${provider}_oauth_state`)
    if (!storedState || storedState !== state) {
      return false
    }
    // Clear the state cookie after verification
    deleteCookie(event, `${provider}_oauth_state`)
    return true
  }

  /**
   * Build OAuth authorization URL
   */
  static buildAuthUrl(
    provider: string,
    redirectUri: string,
    state: string,
    additionalParams?: Record<string, string>,
  ): string {
    const config = getSocialProvider(provider)
    if (!config) {
      throw new Error(`Unknown provider: ${provider}`)
    }

    const clientId = process.env[config.oauthConfig.clientIdEnvVar]
    if (!clientId) {
      throw new Error(`${config.oauthConfig.clientIdEnvVar} not configured`)
    }

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: config.oauthConfig.responseType,
      scope: config.oauthConfig.scopes.join(' '),
      state,
      ...additionalParams,
    })

    return `${config.oauthConfig.authorizationUrl}?${params.toString()}`
  }

  /**
   * Exchange authorization code for access token
   */
  static async exchangeCodeForToken(
    provider: string,
    code: string,
    redirectUri: string,
  ): Promise<{
    accessToken: string
    refreshToken?: string
    expiresIn?: number
    tokenType?: string
    scope?: string
  }> {
    const config = getSocialProvider(provider)
    if (!config) {
      throw new Error(`Unknown provider: ${provider}`)
    }

    const clientId = process.env[config.oauthConfig.clientIdEnvVar]
    const clientSecret = process.env[config.oauthConfig.clientSecretEnvVar]

    if (!clientId || !clientSecret) {
      throw new Error(`OAuth credentials not configured for ${provider}`)
    }

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      client_id: clientId,
      client_secret: clientSecret,
    })

    const response = await fetch(config.oauthConfig.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: params.toString(),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token exchange failed: ${error}`)
    }

    const data = await response.json()

    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresIn: data.expires_in,
      tokenType: data.token_type || 'Bearer',
      scope: data.scope,
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshAccessToken(
    provider: string,
    refreshToken: string,
  ): Promise<{
    accessToken: string
    refreshToken?: string
    expiresIn?: number
    tokenType?: string
  }> {
    const config = getSocialProvider(provider)
    if (!config) {
      throw new Error(`Unknown provider: ${provider}`)
    }

    const clientId = process.env[config.oauthConfig.clientIdEnvVar]
    const clientSecret = process.env[config.oauthConfig.clientSecretEnvVar]

    if (!clientId || !clientSecret) {
      throw new Error(`OAuth credentials not configured for ${provider}`)
    }

    const params = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      client_id: clientId,
      client_secret: clientSecret,
    })

    const response = await fetch(config.oauthConfig.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: params.toString(),
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Token refresh failed: ${error}`)
    }

    const data = await response.json()

    return {
      accessToken: data.access_token,
      refreshToken: data.refresh_token || refreshToken, // Some providers return new refresh token
      expiresIn: data.expires_in,
      tokenType: data.token_type || 'Bearer',
    }
  }

  /**
   * Get user profile from social provider
   */
  static async getUserProfile(
    provider: string,
    accessToken: string,
  ): Promise<{
    id: string
    name?: string
    email?: string
    picture?: string
    username?: string
    [key: string]: any
  }> {
    // Provider-specific profile endpoints
    const profileEndpoints: Record<string, string> = {
      facebook: 'https://graph.facebook.com/v18.0/me?fields=id,name,email,picture',
      instagram: 'https://graph.instagram.com/me?fields=id,username,account_type',
      linkedin: 'https://api.linkedin.com/v2/me?projection=(id,localizedFirstName,localizedLastName,profilePicture(displayImage~:playableStreams))',
      twitter: 'https://api.twitter.com/2/users/me',
      reddit: 'https://oauth.reddit.com/api/v1/me',
      pinterest: 'https://api.pinterest.com/v5/user_account',
      tiktok: 'https://open-api.tiktok.com/user/info/',
      youtube: 'https://www.googleapis.com/oauth2/v2/userinfo',
      threads: 'https://graph.threads.net/v1.0/me',
      snapchat: 'https://adsapi.snapchat.com/v1/me',
      gmail: 'https://www.googleapis.com/oauth2/v2/userinfo',
      outlook: 'https://graph.microsoft.com/v1.0/me',
    }

    const endpoint = profileEndpoints[provider]
    if (!endpoint) {
      throw new Error(`Profile endpoint not configured for ${provider}`)
    }

    const response = await fetch(endpoint, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/json',
      },
    })

    if (!response.ok) {
      const error = await response.text()
      throw new Error(`Failed to fetch user profile: ${error}`)
    }

    const data = await response.json()

    // Normalize profile data across providers
    return this.normalizeProfile(provider, data)
  }

  /**
   * Normalize profile data from different providers
   */
  private static normalizeProfile(provider: string, data: any): any {
    switch (provider) {
      case 'facebook':
        return {
          id: data.id,
          name: data.name,
          email: data.email,
          picture: data.picture?.data?.url,
        }
      case 'instagram':
        return {
          id: data.id,
          username: data.username,
          accountType: data.account_type,
        }
      case 'linkedin':
        return {
          id: data.id,
          name: `${data.localizedFirstName} ${data.localizedLastName}`,
          firstName: data.localizedFirstName,
          lastName: data.localizedLastName,
          picture: data.profilePicture?.['displayImage~']?.elements?.[0]?.identifiers?.[0]?.identifier,
        }
      case 'twitter':
        return {
          id: data.data.id,
          name: data.data.name,
          username: data.data.username,
          picture: data.data.profile_image_url,
        }
      case 'reddit':
        return {
          id: data.id,
          name: data.name,
          username: data.name,
          picture: data.icon_img,
        }
      case 'pinterest':
        return {
          id: data.id,
          username: data.username,
          picture: data.profile_image,
        }
      case 'youtube':
        return {
          id: data.id,
          name: data.name,
          email: data.email,
          picture: data.picture,
        }
      case 'tiktok':
        return {
          id: data.data?.user?.open_id || data.data?.user?.union_id,
          username: data.data?.user?.display_name,
          avatar: data.data?.user?.avatar_url || data.data?.user?.avatar_url_100,
          followerCount: data.data?.user?.follower_count,
          followingCount: data.data?.user?.following_count,
          isVerified: data.data?.user?.is_verified,
        }
      case 'gmail':
        return {
          id: data.id,
          name: data.name,
          email: data.email,
          picture: data.picture,
          verified_email: data.verified_email,
        }
      case 'outlook':
        return {
          id: data.id,
          name: data.displayName,
          email: data.mail || data.userPrincipalName,
          firstName: data.givenName,
          lastName: data.surname,
          jobTitle: data.jobTitle,
          officeLocation: data.officeLocation,
        }
      default:
        return data
    }
  }
}
