import type { Transporter } from 'nodemailer'
import nodemailer from 'nodemailer'

export interface EmailTransporterConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
  requireTLS?: boolean
  tls?: {
    rejectUnauthorized?: boolean
  }
}

export function createTransporter(config: EmailTransporterConfig): Transporter {
  return nodemailer.createTransporter({
    host: config.host,
    port: config.port,
    secure: config.secure,
    auth: config.auth,
    requireTLS: config.requireTLS,
    tls: config.tls,
  })
}

export function getProviderConfig(provider: string): Partial<EmailTransporterConfig> {
  const configs = {
    gmail: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      requireTLS: true,
    },
    outlook: {
      host: 'smtp-mail.outlook.com',
      port: 587,
      secure: false,
      requireTLS: true,
    },
    yahoo: {
      host: 'smtp.mail.yahoo.com',
      port: 587,
      secure: false,
      requireTLS: true,
    },
    icloud: {
      host: 'smtp.mail.me.com',
      port: 587,
      secure: false,
      requireTLS: true,
    },
    zoho: {
      host: 'smtp.zoho.com',
      port: 587,
      secure: false,
      requireTLS: true,
    },
  }

  return configs[provider.toLowerCase()] || {}
}

export async function testEmailConnection(config: EmailTransporterConfig): Promise<{ success: boolean, error?: string }> {
  try {
    const transporter = createTransporter(config)
    await transporter.verify()
    return { success: true }
  }
  catch (error) {
    return {
      success: false,
      error: error.message || 'Connection test failed',
    }
  }
}

export interface OutgoingEmailMessage {
  from: string
  to: string | string[]
  cc?: string | string[]
  bcc?: string | string[]
  subject: string
  text?: string
  html?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

export async function sendEmail(
  transporter: Transporter,
  message: OutgoingEmailMessage,
): Promise<{ success: boolean, messageId?: string, error?: string }> {
  try {
    const info = await transporter.sendMail(message)
    return {
      success: true,
      messageId: info.messageId,
    }
  }
  catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to send email',
    }
  }
}
