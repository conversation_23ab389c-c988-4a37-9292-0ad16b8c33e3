<script setup lang="ts">
import type { CalendarCreationData } from '~/composables/useCalendarManagement'

// Define EventCategory type locally since it's simple
type EventCategory = 'customer' | 'internal' | 'team' | 'personal' | 'none'

// Define a simple event creation function instead of using the full composable
const { currentUser, currentWorkspace } = useAuth()
const { $clientFirestore } = useNuxtApp()
const toaster = useToast()

// Calendar management
const {
  calendars,
  selectedCalendarId,
  visibleCalendars,
  loading: calendarsLoading,
  createCalendar,
  updateCalendar,
  deleteCalendar,
  toggleCalendarVisibility,
  updateDefaultCalendar,
  calendarColors,
  subscribeToCalendars,
} = useCalendarManagement()

// Google Calendar integration - reusing from integrations page
const {
  isConnecting: isGoogleCalendarConnecting,
  isConnected: isGoogleCalendarConnected,
  connectGoogleCalendar,
  disconnectGoogleCalendar,
  syncFromGoogle,
  syncToGoogle,
  currentIntegration: googleCalendarIntegration,
  syncStatus: googleCalendarSyncStatus,
} = useGoogleCalendar()

// UI state
const showCreateCalendarModal = ref(false)
const showCreateEventModal = ref(false)
const showIntegrationsModal = ref(false)
const showCalendarMenu = ref<string | null>(null)

// Form data
const newCalendarForm = ref<CalendarCreationData>({
  name: '',
  description: '',
  color: calendarColors[0].value,
  isDefault: false,
})

// Event form data
const newEventForm = ref({
  title: '',
  description: '',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  category: 'none' as EventCategory,
  location: '',
})

// Simple event creation function without the full composable
async function createEvent(eventData: any) {
  if (!currentUser.value || !currentWorkspace.value) {
    throw new Error('Authentication required')
  }

  try {
    // Import Firestore functions dynamically
    const { addDoc, collection, serverTimestamp, Timestamp } = await import('firebase/firestore')

    const newEvent = {
      title: eventData.title,
      description: eventData.description || '',
      location: eventData.location || '',
      category: eventData.category || 'none',
      userId: currentUser.value.id,
      workspaceId: currentWorkspace.value.id,
      calendarId: selectedCalendarId.value !== 'all' ? selectedCalendarId.value : null,
      startDate: Timestamp.fromDate(eventData.startDate),
      endDate: Timestamp.fromDate(eventData.endDate),
      duration: eventData.duration || 60,
      participants: eventData.participants || [],
      features: eventData.features || {},
      status: 'scheduled',
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    }

    const docRef = await addDoc(collection($clientFirestore, 'calendar_events'), newEvent)

    toaster.add({
      title: 'Event Created',
      description: `Event "${eventData.title}" has been created successfully`,
      color: 'green',
      icon: 'heroicons:check-circle',
    })

    return docRef.id
  }
  catch (error: any) {
    console.error('Error creating event:', error)
    toaster.add({
      title: 'Error Creating Event',
      description: error.message,
      color: 'red',
      icon: 'heroicons:x-circle',
    })
    throw error
  }
}

// Initialize calendar subscription
onMounted(() => {
  subscribeToCalendars()

  // Set default dates for new event
  const today = new Date()
  newEventForm.value.startDate = today.toISOString().split('T')[0]
  newEventForm.value.endDate = today.toISOString().split('T')[0]
  newEventForm.value.startTime = '09:00'
  newEventForm.value.endTime = '10:00'
})

// Create calendar
async function handleCreateCalendar() {
  if (!newCalendarForm.value.name.trim())
    return

  try {
    await createCalendar(newCalendarForm.value)

    // Reset form
    newCalendarForm.value = {
      name: '',
      description: '',
      color: calendarColors[0].value,
      isDefault: false,
    }

    showCreateCalendarModal.value = false
  }
  catch (error) {
    console.error('Error creating calendar:', error)
  }
}

// Create event
async function handleCreateEvent() {
  if (!newEventForm.value.title.trim())
    return

  try {
    // Combine date and time
    const startDateTime = new Date(`${newEventForm.value.startDate}T${newEventForm.value.startTime}`)
    const endDateTime = new Date(`${newEventForm.value.endDate}T${newEventForm.value.endTime}`)

    // Calculate duration in minutes
    const duration = Math.round((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60))

    await createEvent({
      title: newEventForm.value.title,
      description: newEventForm.value.description,
      location: newEventForm.value.location,
      startDate: startDateTime,
      endDate: endDateTime,
      duration,
      category: newEventForm.value.category,
      participants: [],
      features: {},
    })

    // Reset form
    newEventForm.value = {
      title: '',
      description: '',
      startDate: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endDate: new Date().toISOString().split('T')[0],
      endTime: '10:00',
      category: 'none',
      location: '',
    }

    showCreateEventModal.value = false
  }
  catch (error) {
    console.error('Error creating event:', error)
  }
}

// Handle calendar selection
function handleCalendarSelect(calendarId: string) {
  selectedCalendarId.value = calendarId
  showCalendarMenu.value = null
}

// Handle Google Calendar connection
async function handleConnectGoogleCalendar() {
  try {
    await connectGoogleCalendar()
    showIntegrationsModal.value = false
  }
  catch (error) {
    console.error('Error connecting Google Calendar:', error)
  }
}

// Handle Google Calendar sync
async function handleGoogleSync(direction: 'import' | 'export' | 'bidirectional' = 'bidirectional') {
  try {
    if (direction === 'import' || direction === 'bidirectional') {
      await syncFromGoogle()
    }
    if (direction === 'export' || direction === 'bidirectional') {
      await syncToGoogle()
    }
  }
  catch (error) {
    console.error('Error syncing with Google Calendar:', error)
  }
}

// Toggle calendar menu
function toggleCalendarMenu(calendarId: string) {
  showCalendarMenu.value = showCalendarMenu.value === calendarId ? null : calendarId
}

// Close menus when clicking outside
// Note: onClickOutside should be auto-imported, if not available we'll handle it differently
try {
  onClickOutside(showCalendarMenu, () => {
    showCalendarMenu.value = null
  })
}
catch {
  // Fallback: use document click listener
  onMounted(() => {
    const handleClickOutside = (event: Event) => {
      if (showCalendarMenu.value && !(event.target as Element)?.closest('.calendar-menu')) {
        showCalendarMenu.value = null
      }
    }
    document.addEventListener('click', handleClickOutside)
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
  })
}
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Header with actions -->
    <div class="p-4 border-b border-muted-200 dark:border-muted-800">
      <div class="flex items-center justify-between mb-4">
        <BaseHeading size="sm" weight="medium" class="text-muted-900 dark:text-white">
          My Calendars
        </BaseHeading>
        <div class="flex items-center gap-2">
          <!-- Create Event Button -->
          <BaseButton
            size="icon-sm"
            rounded="md"
            variant="ghost"
            @click="showCreateEventModal = true"
          >
            <Icon name="lucide:plus" class="size-4" />
          </BaseButton>

          <!-- Add Calendar Button -->
          <BaseButton
            size="icon-sm"
            rounded="md"
            variant="ghost"
            @click="showCreateCalendarModal = true"
          >
            <Icon name="lucide:calendar-plus" class="size-4" />
          </BaseButton>

          <!-- Integrations Button -->
          <BaseButton
            size="icon-sm"
            rounded="md"
            variant="ghost"
            @click="showIntegrationsModal = true"
          >
            <Icon name="lucide:settings" class="size-4" />
          </BaseButton>
        </div>
      </div>

      <!-- Show All Option -->
      <div class="space-y-1">
        <label
          class="flex items-center gap-3 p-2 rounded-lg cursor-pointer hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors"
          :class="{
            'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800': selectedCalendarId === 'all',
          }"
        >
          <BaseRadio
            v-model="selectedCalendarId"
            value="all"
            name="calendar-selection"
            color="primary"
          />
          <span class="text-sm font-medium text-muted-900 dark:text-white">
            Show All Calendars
          </span>
        </label>
      </div>
    </div>

    <!-- Calendar List -->
    <div class="flex-1 overflow-y-auto p-4">
      <div v-if="calendarsLoading" class="space-y-2">
        <div class="h-10 rounded-lg bg-muted-200 dark:bg-muted-800 animate-pulse" />
        <div class="h-10 rounded-lg bg-muted-200 dark:bg-muted-800 animate-pulse" />
        <div class="h-10 rounded-lg bg-muted-200 dark:bg-muted-800 animate-pulse" />
      </div>

      <div v-else-if="calendars.length === 0" class="text-center py-8">
        <Icon name="lucide:calendar" class="size-12 text-muted-400 mx-auto mb-3" />
        <BaseHeading size="sm" class="text-muted-600 dark:text-muted-400 mb-2">
          No calendars yet
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 mb-4">
          Create your first calendar to get started
        </BaseParagraph>
        <BaseButton size="sm" @click="showCreateCalendarModal = true">
          <Icon name="lucide:plus" class="size-4 mr-2" />
          Create Calendar
        </BaseButton>
      </div>

      <div v-else class="space-y-1">
        <div
          v-for="calendar in calendars"
          :key="calendar.id"
          class="relative"
        >
          <label
            class="flex items-center gap-3 p-2 rounded-lg cursor-pointer hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors"
            :class="{
              'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800': selectedCalendarId === calendar.id,
            }"
          >
            <BaseRadio
              v-model="selectedCalendarId"
              :value="calendar.id"
              name="calendar-selection"
              color="primary"
            />

            <!-- Calendar color indicator -->
            <div
              class="size-3 rounded-full border border-white dark:border-muted-900"
              :style="{ backgroundColor: calendar.color }"
            />

            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-muted-900 dark:text-white truncate">
                  {{ calendar.name }}
                </span>
                <Icon
                  v-if="calendar.isDefault"
                  name="lucide:star"
                  class="size-3 text-yellow-500 fill-current"
                />
                <Icon
                  v-if="calendar.provider"
                  :name="calendar.provider === 'google' ? 'logos:google-icon' : 'lucide:link'"
                  class="size-3"
                />
              </div>
              <span
                v-if="calendar.description"
                class="text-xs text-muted-500 truncate block"
              >
                {{ calendar.description }}
              </span>
            </div>

            <!-- Visibility toggle -->
            <BaseButton
              size="icon-xs"
              rounded="md"
              variant="ghost"
              @click.prevent="toggleCalendarVisibility(calendar.id)"
            >
              <Icon
                :name="calendar.isVisible ? 'lucide:eye' : 'lucide:eye-off'"
                class="size-3"
                :class="calendar.isVisible ? 'text-muted-600' : 'text-muted-400'"
              />
            </BaseButton>

            <!-- Calendar menu -->
            <BaseButton
              size="icon-xs"
              rounded="md"
              variant="ghost"
              @click.prevent="toggleCalendarMenu(calendar.id)"
            >
              <Icon name="lucide:more-horizontal" class="size-3" />
            </BaseButton>
          </label>

          <!-- Calendar menu dropdown -->
          <div
            v-if="showCalendarMenu === calendar.id"
            class="absolute right-2 top-12 z-50 w-48 bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-800 rounded-lg shadow-lg py-1"
          >
            <button
              class="w-full px-3 py-2 text-left text-sm hover:bg-muted-50 dark:hover:bg-muted-800 flex items-center gap-2"
              @click="updateDefaultCalendar(calendar.id)"
            >
              <Icon name="lucide:star" class="size-4" />
              Set as Default
            </button>
            <button
              class="w-full px-3 py-2 text-left text-sm hover:bg-muted-50 dark:hover:bg-muted-800 flex items-center gap-2"
              @click="deleteCalendar(calendar.id)"
            >
              <Icon name="lucide:trash-2" class="size-4 text-red-500" />
              <span class="text-red-500">Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Event Modal -->
    <BaseModal
      v-model="showCreateEventModal"
      title="Create New Event"
      size="md"
    >
      <form class="space-y-4" @submit.prevent="handleCreateEvent">
        <BaseField label="Event Title" required>
          <BaseInput
            v-model="newEventForm.title"
            placeholder="Enter event title"
            required
          />
        </BaseField>

        <div class="grid grid-cols-2 gap-4">
          <BaseField label="Start Date" required>
            <BaseInput
              v-model="newEventForm.startDate"
              type="date"
              required
            />
          </BaseField>

          <BaseField label="Start Time" required>
            <BaseInput
              v-model="newEventForm.startTime"
              type="time"
              required
            />
          </BaseField>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <BaseField label="End Date" required>
            <BaseInput
              v-model="newEventForm.endDate"
              type="date"
              required
            />
          </BaseField>

          <BaseField label="End Time" required>
            <BaseInput
              v-model="newEventForm.endTime"
              type="time"
              required
            />
          </BaseField>
        </div>

        <BaseField label="Category">
          <BaseSelect v-model="newEventForm.category">
            <BaseSelectItem value="none">
              No Category
            </BaseSelectItem>
            <BaseSelectItem value="personal">
              Personal
            </BaseSelectItem>
            <BaseSelectItem value="team">
              Team
            </BaseSelectItem>
            <BaseSelectItem value="internal">
              Internal
            </BaseSelectItem>
            <BaseSelectItem value="customer">
              Customer
            </BaseSelectItem>
          </BaseSelect>
        </BaseField>

        <BaseField label="Location">
          <BaseInput
            v-model="newEventForm.location"
            placeholder="Event location (optional)"
          />
        </BaseField>

        <BaseField label="Description">
          <BaseTextarea
            v-model="newEventForm.description"
            placeholder="Event description (optional)"
            rows="3"
          />
        </BaseField>

        <div class="flex justify-end gap-2 pt-4">
          <BaseButton
            type="button"
            variant="ghost"
            @click="showCreateEventModal = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            type="submit"
            color="primary"
            :disabled="!newEventForm.title.trim()"
          >
            Create Event
          </BaseButton>
        </div>
      </form>
    </BaseModal>

    <!-- Create Calendar Modal -->
    <BaseModal
      v-model="showCreateCalendarModal"
      title="Create New Calendar"
      size="md"
    >
      <form class="space-y-4" @submit.prevent="handleCreateCalendar">
        <BaseField label="Calendar Name" required>
          <BaseInput
            v-model="newCalendarForm.name"
            placeholder="Enter calendar name"
            required
          />
        </BaseField>

        <BaseField label="Description">
          <BaseTextarea
            v-model="newCalendarForm.description"
            placeholder="Optional description"
            rows="2"
          />
        </BaseField>

        <BaseField label="Color">
          <div class="flex flex-wrap gap-2">
            <label
              v-for="color in calendarColors"
              :key="color.value"
              class="cursor-pointer"
            >
              <input
                v-model="newCalendarForm.color"
                type="radio"
                :value="color.value"
                class="sr-only"
              >
              <div
                class="size-8 rounded-full border-2 transition-all"
                :class="[
                  color.bg,
                  newCalendarForm.color === color.value
                    ? 'border-primary-500 ring-2 ring-primary-200 dark:ring-primary-800'
                    : 'border-muted-300 dark:border-muted-600',
                ]"
              />
            </label>
          </div>
        </BaseField>

        <BaseField>
          <BaseCheckbox
            v-model="newCalendarForm.isDefault"
            label="Set as default calendar"
          />
        </BaseField>

        <div class="flex justify-end gap-2 pt-4">
          <BaseButton
            type="button"
            variant="ghost"
            @click="showCreateCalendarModal = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            type="submit"
            color="primary"
            :disabled="!newCalendarForm.name.trim()"
          >
            Create Calendar
          </BaseButton>
        </div>
      </form>
    </BaseModal>

    <!-- Integrations Modal -->
    <BaseModal
      v-model="showIntegrationsModal"
      title="Calendar Integrations"
      size="lg"
    >
      <div class="space-y-6">
        <!-- Google Calendar Integration -->
        <div class="border border-muted-200 dark:border-muted-800 rounded-lg p-4">
          <div class="flex items-start gap-4">
            <div class="size-10 bg-white rounded-lg border border-muted-200 dark:border-muted-700 flex items-center justify-center">
              <Icon name="logos:google-calendar" class="size-6" />
            </div>

            <div class="flex-1">
              <BaseHeading size="sm" class="text-muted-900 dark:text-white mb-1">
                Google Calendar
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 mb-3">
                Sync your events with Google Calendar
              </BaseParagraph>

              <div v-if="isGoogleCalendarConnected" class="space-y-3">
                <div class="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                  <Icon name="lucide:check-circle" class="size-4" />
                  Connected
                </div>

                <div class="flex gap-2">
                  <BaseButton
                    size="sm"
                    variant="outline"
                    :loading="googleCalendarSyncStatus === 'syncing'"
                    @click="handleGoogleSync('bidirectional')"
                  >
                    <Icon name="lucide:refresh-cw" class="size-4 mr-2" />
                    Sync Now
                  </BaseButton>

                  <BaseButton
                    size="sm"
                    variant="ghost"
                    color="red"
                    @click="disconnectGoogleCalendar"
                  >
                    Disconnect
                  </BaseButton>
                </div>
              </div>

              <div v-else>
                <BaseButton
                  size="sm"
                  color="primary"
                  :loading="isGoogleCalendarConnecting"
                  @click="handleConnectGoogleCalendar"
                >
                  <Icon name="lucide:link" class="size-4 mr-2" />
                  Connect Google Calendar
                </BaseButton>
              </div>
            </div>
          </div>
        </div>

        <!-- Other integrations placeholder -->
        <div class="border border-muted-200 dark:border-muted-800 rounded-lg p-4 opacity-50">
          <div class="flex items-start gap-4">
            <div class="size-10 bg-white rounded-lg border border-muted-200 dark:border-muted-700 flex items-center justify-center">
              <Icon name="simple-icons:microsoftoutlook" class="size-6" />
            </div>

            <div class="flex-1">
              <BaseHeading size="sm" class="text-muted-900 dark:text-white mb-1">
                Outlook Calendar
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 mb-3">
                Sync with Microsoft Outlook Calendar
              </BaseParagraph>

              <BaseButton size="sm" disabled>
                Coming Soon
              </BaseButton>
            </div>
          </div>
        </div>

        <div class="border border-muted-200 dark:border-muted-800 rounded-lg p-4 opacity-50">
          <div class="flex items-start gap-4">
            <div class="size-10 bg-white rounded-lg border border-muted-200 dark:border-muted-700 flex items-center justify-center">
              <Icon name="logos:apple" class="size-6" />
            </div>

            <div class="flex-1">
              <BaseHeading size="sm" class="text-muted-900 dark:text-white mb-1">
                Apple Calendar
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 mb-3">
                Sync with Apple iCloud Calendar
              </BaseParagraph>

              <BaseButton size="sm" disabled>
                Coming Soon
              </BaseButton>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end pt-6">
        <BaseButton
          variant="ghost"
          @click="showIntegrationsModal = false"
        >
          Close
        </BaseButton>
      </div>
    </BaseModal>
  </div>
</template>

<style scoped>
/* Custom styles for the calendar sidebar */
.calendar-sidebar {
  min-width: 280px;
}
</style>
