<script setup lang="ts">
export interface EmailMessage {
  id: number
  sender: {
    name: string
    email: string
    photo: string
  }
  title: string
  abstract: string
  content: string
  attachments: Array<{
    name: string
    size: string
    type: string
  }>
  time: string
  status: 'read' | 'unread'
}

interface Props {
  messages: EmailMessage[]
  activeMessageId?: number
  search?: string
}

interface Emits {
  (e: 'message-selected', messageId: number): void
  (e: 'panel-activated'): void
}

const props = withDefaults(defineProps<Props>(), {
  activeMessageId: 1,
  search: '',
})

const emit = defineEmits<Emits>()

// Filter messages based on search
const filteredMessages = computed(() => {
  if (!props.search.trim()) {
    return props.messages
  }

  const searchTerm = props.search.toLowerCase()
  return props.messages.filter(message =>
    message.title.toLowerCase().includes(searchTerm)
    || message.abstract.toLowerCase().includes(searchTerm)
    || message.sender.name.toLowerCase().includes(searchTerm)
    || message.sender.email.toLowerCase().includes(searchTerm),
  )
})

// Handle message selection
function selectMessage(messageId: number) {
  emit('message-selected', messageId)
  emit('panel-activated')
}
</script>

<template>
  <div class="bg-muted-50 dark:bg-muted-900 flex size-full flex-col pt-3 lg:w-full">
    <!-- Head (search) -->
    <div class="h-16 w-full px-4 sm:px-8">
      <TairoInput
        :model-value="search"
        rounded="lg"
        placeholder="Search"
        icon="lucide:search"
        @update:model-value="(value) => $emit('update:search', value)"
      />
    </div>

    <ul class="nui-slimscroll-opaque grow space-y-2 overflow-y-auto ps-4 pe-2 me-2 pb-8 sm:ps-8 sm:pe-4 sm:me-4 pt-4">
      <li
        v-for="message in filteredMessages"
        :key="message.id"
        class="duration 300 cursor-pointer rounded-xl p-4 transition-colors sm:p-6"
        :class="[
          activeMessageId === message.id
            ? 'bg-muted-200/60 dark:bg-muted-950/60'
            : 'hover:bg-muted-100 dark:hover:bg-muted-950/30',
          message.status === 'read' ? 'opacity-60 hover:opacity-100' : '',
        ]"
        role="button"
        tabindex="0"
        @click="selectMessage(message.id)"
        @keydown.enter="selectMessage(message.id)"
        @keydown.space.prevent="selectMessage(message.id)"
      >
        <div>
          <BaseHeading
            size="md"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white mb-2"
          >
            {{ message.title }}
          </BaseHeading>
          <BaseParagraph
            size="sm"
            lead="tight"
            class="text-muted-500 dark:text-muted-400 pointer-events-none"
          >
            {{ message.abstract }}
          </BaseParagraph>
        </div>
        <div class="mt-3 flex items-center gap-2">
          <BaseAvatar
            size="xxs"
            rounded="none"
            mask="blob"
            :src="message.sender.photo"
            class="pointer-events-none"
          />
          <div class="pointer-events-none">
            <BaseHeading
              size="sm"
              weight="medium"
              lead="tight"
            >
              <span>{{ message.sender.name }}</span>
            </BaseHeading>
          </div>
          <div class="pointer-events-none ms-auto">
            <span class="text-muted-400 font-sans text-xs">
              {{ message.time }}
            </span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
