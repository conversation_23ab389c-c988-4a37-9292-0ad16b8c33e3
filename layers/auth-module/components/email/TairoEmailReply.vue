<script setup lang="ts">
interface Props {
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'send-reply', content: string): void
  (e: 'attach-file'): void
  (e: 'attach-image'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Type your reply here...',
  disabled: false,
})

const emit = defineEmits<Emits>()

// Reply content state
const replyContent = ref('')

// Handle send reply
function sendReply() {
  if (!replyContent.value.trim() || props.disabled) {
    return
  }

  emit('send-reply', replyContent.value.trim())
  replyContent.value = '' // Clear after sending
}

// Handle keyboard shortcuts
function handleKeydown(event: KeyboardEvent) {
  // Send on Ctrl/Cmd + Enter
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    sendReply()
  }
}
</script>

<template>
  <div class="relative flex h-44 w-full items-center justify-center px-8">
    <div
      class="border-muted-300 dark:border-muted-800/60 bg-muted-100 dark:bg-muted-950 focus-within:outline-muted-200 dark:focus-within:outline-muted-700 mb-3 w-full rounded-xl border outline-none outline-offset-4 transition-all duration-300 focus-within:outline-dashed focus-within:outline-2"
    >
      <textarea
        v-model="replyContent"
        class="bg-muted-100 dark:bg-muted-950 placeholder:text-muted-300 dark:placeholder:text-muted-600 w-full resize-none rounded-2xl p-3 font-sans outline-none"
        :placeholder="placeholder"
        :disabled="disabled"
        rows="2"
        @keydown="handleKeydown"
      />
      <div class="flex items-center justify-between p-3">
        <div class="flex items-center gap-2">
          <BaseButton
            size="icon-md"
            variant="default"
            rounded="lg"
            class="text-muted-500 dark:text-muted-100 flex size-8 items-center justify-center"
            :disabled="disabled"
            @click="emit('attach-file')"
          >
            <Icon name="solar:paperclip-linear" class="size-4" />
          </BaseButton>
          <BaseButton
            size="icon-md"
            variant="default"
            rounded="lg"
            class="text-muted-500 dark:text-muted-100 flex size-8 items-center justify-center"
            :disabled="disabled"
            @click="emit('attach-image')"
          >
            <Icon name="solar:gallery-minimalistic-linear" class="size-4" />
          </BaseButton>
        </div>
        <BaseButton
          rounded="lg"
          variant="dark"
          :disabled="!replyContent.trim() || disabled"
          @click="sendReply"
        >
          <span>Send Reply</span>
        </BaseButton>
      </div>
    </div>
  </div>
</template>
