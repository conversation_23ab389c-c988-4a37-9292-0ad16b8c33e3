<script setup lang="ts">
import type { EmailAccount } from '~/types/integration'

interface Props {
  accounts: EmailAccount[]
  activeAccountId?: string | null
  loading?: boolean
  search?: string
  filter?: string
  emails?: any[]
  unreadCount?: number
}

interface Emits {
  (e: 'account-selected', accountId: string): void
  (e: 'add-account'): void
  (e: 'sync-account', accountId: string): void
  (e: 'manage-integrations'): void
  (e: 'search-updated', search: string): void
  (e: 'filter-updated', filter: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  search: '',
  filter: 'all',
  emails: () => [],
  unreadCount: 0,
})

const emit = defineEmits<Emits>()

// Email folders/sections
const folders = [
  { id: 'inbox', name: 'Inbox', icon: 'lucide:inbox', count: null },
  { id: 'sent', name: '<PERSON><PERSON>', icon: 'lucide:send', count: null },
  { id: 'drafts', name: 'Drafts', icon: 'lucide:file-edit', count: null },
  { id: 'starred', name: 'Starr<PERSON>', icon: 'lucide:star', count: null },
  { id: 'trash', name: 'Trash', icon: 'lucide:trash-2', count: null },
]

// Internal search state
const searchValue = ref(props.search || '')

// Watch for prop changes
watch(() => props.search, (newSearch) => {
  searchValue.value = newSearch || ''
})

// Watch for search input changes
watch(searchValue, (newValue) => {
  emit('search-updated', newValue)
})

// Get selected folder from filter prop
const selectedFolder = computed(() => props.filter || 'all')

// Get provider icon
function getProviderIcon(provider: string) {
  const icons = {
    gmail: 'logos:google-gmail',
    outlook: 'simple-icons:microsoftoutlook',
    yahoo: 'simple-icons:yahoo',
    imap: 'lucide:mail',
    exchange: 'simple-icons:microsoftexchange',
    custom: 'lucide:server',
  }
  return icons[provider as keyof typeof icons] || 'lucide:mail'
}

// Get sync status color
function getSyncStatusColor(status: string) {
  switch (status) {
    case 'syncing': return 'warning'
    case 'error': return 'danger'
    case 'idle': return 'success'
    default: return 'muted'
  }
}

// Format last sync time
function formatLastSync(lastSyncAt?: string) {
  if (!lastSyncAt)
    return 'Never synced'

  const date = new Date(lastSyncAt)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / 60000)

  if (diffMins < 1)
    return 'Just now'
  if (diffMins < 60)
    return `${diffMins}m ago`
  if (diffMins < 1440)
    return `${Math.floor(diffMins / 60)}h ago`
  return date.toLocaleDateString()
}

// Handle account selection
function selectAccount(accountId: string) {
  emit('account-selected', accountId)
}

// Handle sync
function syncAccount(accountId: string, event: Event) {
  event.stopPropagation()
  emit('sync-account', accountId)
}

// Get count for each filter
function getFilterCount(filterId: string): number {
  if (!props.emails)
    return 0

  switch (filterId) {
    case 'inbox':
      return props.emails.length
    case 'unread':
      return props.unreadCount || 0
    case 'starred':
      return props.emails.filter(email => email.flags?.flagged).length
    case 'sent':
    case 'drafts':
    case 'trash':
      return 0 // These would need to be implemented based on your email system
    default:
      return 0
  }
}
</script>

<template>
  <div class="flex h-full flex-col bg-white dark:bg-muted-900">
    <!-- Header -->
    <div class="border-b border-muted-200 p-4 dark:border-muted-700">
      <div class="flex items-center justify-between mb-4">
        <BaseHeading size="lg" weight="semibold">
          Inbox
        </BaseHeading>
        <BaseButton
          size="sm"
          variant="outline"
          @click="emit('add-account')"
        >
          <Icon name="lucide:plus" class="size-4" />
          Add Account
        </BaseButton>
      </div>

      <!-- Search -->
      <BaseInput
        v-model="searchValue"
        placeholder="Search emails..."
        size="sm"
        class="w-full"
      >
        <template #icon>
          <Icon name="lucide:search" class="size-4" />
        </template>
      </BaseInput>
    </div>

    <!-- Folders/Filters -->
    <div class="border-b border-muted-200 p-4 dark:border-muted-700">
      <nav class="space-y-1">
        <button
          v-for="folder in folders"
          :key="folder.id"
          class="flex w-full items-center justify-between rounded-lg px-3 py-2 text-left text-sm transition-colors hover:bg-muted-100 dark:hover:bg-muted-800"
          :class="[
            selectedFolder === folder.id
              ? 'bg-primary-100 text-primary-600 dark:bg-primary-900/20 dark:text-primary-400'
              : 'text-muted-600 dark:text-muted-400',
          ]"
          @click="emit('filter-updated', folder.id)"
        >
          <div class="flex items-center gap-3">
            <Icon :name="folder.icon" class="size-4" />
            <span>{{ folder.name }}</span>
          </div>
          <span
            v-if="getFilterCount(folder.id)"
            class="rounded-full bg-muted-200 px-2 py-0.5 text-xs dark:bg-muted-700"
          >
            {{ getFilterCount(folder.id) }}
          </span>
        </button>
      </nav>
    </div>

    <!-- Email Accounts -->
    <div class="flex-1 overflow-y-auto p-4">
      <div class="mb-3 flex items-center justify-between">
        <BaseHeading size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
          Email Accounts
        </BaseHeading>
        <BaseButton
          size="xs"
          variant="ghost"
          @click="emit('manage-integrations')"
        >
          <Icon name="lucide:settings" class="size-3" />
          Manage
        </BaseButton>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="space-y-3">
        <div
          v-for="i in 3"
          :key="i"
          class="animate-pulse rounded-lg bg-muted-200 p-3 dark:bg-muted-800"
        >
          <div class="h-4 w-3/4 rounded bg-muted-300 dark:bg-muted-700" />
          <div class="mt-2 h-3 w-1/2 rounded bg-muted-300 dark:bg-muted-700" />
        </div>
      </div>

      <!-- Accounts List -->
      <div v-else-if="accounts.length > 0" class="space-y-2">
        <div
          v-for="account in accounts"
          :key="account.id"
          class="group relative cursor-pointer rounded-lg border border-muted-200 p-3 transition-all hover:border-muted-300 hover:shadow-sm dark:border-muted-700 dark:hover:border-muted-600"
          :class="[
            account.id === activeAccountId
              ? 'border-primary-300 bg-primary-50 dark:border-primary-600 dark:bg-primary-900/10'
              : 'bg-white dark:bg-muted-800',
          ]"
          @click="selectAccount(account.id)"
        >
          <!-- Account Header -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="relative">
                <Icon
                  :name="getProviderIcon(account.provider)"
                  class="size-5"
                />
                <!-- Connection Status -->
                <div
                  class="absolute -bottom-1 -right-1 size-2 rounded-full border border-white dark:border-muted-800"
                  :class="[
                    account.isConnected
                      ? 'bg-success-500'
                      : 'bg-danger-500',
                  ]"
                />
              </div>
              <div class="flex-1 min-w-0">
                <div class="truncate text-sm font-medium text-muted-900 dark:text-muted-100">
                  {{ account.displayName }}
                </div>
                <div class="truncate text-xs text-muted-500 dark:text-muted-400">
                  {{ account.email }}
                </div>
              </div>
            </div>

            <!-- Sync Button -->
            <BaseButton
              size="xs"
              variant="ghost"
              :loading="account.syncStatus === 'syncing'"
              @click="syncAccount(account.id, $event)"
            >
              <Icon
                name="lucide:refresh-cw"
                class="size-3"
                :class="{ 'animate-spin': account.syncStatus === 'syncing' }"
              />
            </BaseButton>
          </div>

          <!-- Account Status -->
          <div class="mt-2 flex items-center justify-between text-xs">
            <div class="flex items-center gap-2">
              <div
                class="size-2 rounded-full"
                :class="[
                  `bg-${getSyncStatusColor(account.syncStatus)}-500`,
                ]"
              />
              <span class="text-muted-500 dark:text-muted-400">
                {{ formatLastSync(account.lastSyncAt) }}
              </span>
            </div>

            <!-- Unread Count -->
            <div
              v-if="account.unreadCount && account.unreadCount > 0"
              class="rounded-full bg-primary-500 px-2 py-0.5 text-xs font-medium text-white"
            >
              {{ account.unreadCount > 99 ? '99+' : account.unreadCount }}
            </div>
          </div>

          <!-- Quota Info (if available) -->
          <div
            v-if="account.quotaInfo"
            class="mt-2 text-xs text-muted-500 dark:text-muted-400"
          >
            <div class="flex items-center justify-between">
              <span>Storage</span>
              <span>{{ account.quotaInfo.percentage }}%</span>
            </div>
            <div class="mt-1 h-1 rounded-full bg-muted-200 dark:bg-muted-700">
              <div
                class="h-1 rounded-full transition-all"
                :class="[
                  account.quotaInfo.percentage > 80
                    ? 'bg-danger-500'
                    : account.quotaInfo.percentage > 60
                      ? 'bg-warning-500'
                      : 'bg-success-500',
                ]"
                :style="{ width: `${account.quotaInfo.percentage}%` }"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-8">
        <Icon name="lucide:mail" class="mx-auto size-12 text-muted-400 dark:text-muted-600" />
        <BaseHeading size="sm" class="mt-4 text-muted-600 dark:text-muted-400">
          No Email Accounts
        </BaseHeading>
        <BaseParagraph size="sm" class="mt-2 text-muted-500 dark:text-muted-400">
          Add your first email account to get started
        </BaseParagraph>
        <BaseButton
          size="sm"
          class="mt-4"
          @click="emit('add-account')"
        >
          <Icon name="lucide:plus" class="size-4" />
          Add Email Account
        </BaseButton>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t border-muted-200 p-4 dark:border-muted-700">
      <BaseButton
        variant="outline"
        size="sm"
        class="w-full"
        @click="emit('manage-integrations')"
      >
        <Icon name="lucide:settings" class="size-4" />
        Manage Integrations
      </BaseButton>
    </div>
  </div>
</template>
