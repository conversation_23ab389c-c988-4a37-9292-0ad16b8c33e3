<script setup lang="ts">
import type { EmailMessage } from './TairoEmailList.vue'

interface Props {
  message?: EmailMessage
  panelActive?: boolean
}

interface Emits {
  (e: 'panel-closed'): void
  (e: 'reply-clicked'): void
  (e: 'bookmark-clicked'): void
  (e: 'delete-clicked'): void
  (e: 'attachment-download', attachment: { name: string, size: string, type: string }): void
  (e: 'send-reply', content: string): void
  (e: 'attach-file'): void
  (e: 'attach-image'): void
}

const props = withDefaults(defineProps<Props>(), {
  panelActive: false,
})

const emit = defineEmits<Emits>()

// Handle attachment download
function downloadAttachment(attachment: { name: string, size: string, type: string }) {
  emit('attachment-download', attachment)
}

// Get attachment icon based on file type
function getAttachmentIcon(type: string) {
  const iconMap: Record<string, string> = {
    pdf: '/img/icons/files/pdf.svg',
    zip: '/img/icons/files/zip-format.svg',
    ai: '/img/icons/files/ai.svg',
    sheet: '/img/icons/files/sheet.svg',
    doc: '/img/icons/files/doc-2.svg',
  }
  return iconMap[type] || '/img/icons/files/doc-2.svg'
}

function getAttachmentAlt(type: string) {
  const altMap: Record<string, string> = {
    pdf: 'Pdf file',
    zip: 'Zip file',
    ai: 'AI file',
    sheet: 'Sheet',
    doc: 'Document',
  }
  return altMap[type] || 'Document'
}
</script>

<template>
  <div
    class="bg-muted-50 dark:bg-muted-900 border-muted-200 dark:border-muted-800/60 fixed end-0 top-0 z-10 flex h-full flex-col border-s transition-transform duration-300 lg:static lg:grow"
    :class="
      panelActive
        ? 'translate-x-0'
        : 'translate-x-full lg:translate-x-0'
    "
  >
    <!-- Toolbar -->
    <div class="mx-auto w-full lg:px-10">
      <div class="relative z-10 flex lg:hidden h-16 w-full items-center px-8">
        <div class="ms-auto text-muted-700 dark:text-muted-300 flex items-center gap-2">
          <button
            type="button"
            class="flex size-9 items-center justify-center"
            @click="emit('panel-closed')"
          >
            <Icon name="lucide:x" class="size-5" />
          </button>
        </div>
      </div>

      <!-- Message header -->
      <div
        class="border-muted-200 dark:border-muted-800 flex h-20 flex-col justify-between border-b px-8 lg:flex-row lg:items-center"
      >
        <div class="flex items-center gap-x-4">
          <div class="hidden lg:block">
            <BaseAvatar
              size="sm"
              :src="message?.sender.photo"
              :alt="message?.sender.name"
            />
          </div>
          <div class="block lg:hidden">
            <BaseAvatar
              size="sm"
              :src="message?.sender.photo"
              :alt="message?.sender.name"
            />
          </div>
          <div class="flex flex-col">
            <BaseHeading size="md" weight="medium" lead="tight" class="text-muted-900 dark:text-white">
              {{ message?.sender.name }}
            </BaseHeading>
            <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
              {{ message?.sender.email }}
            </BaseParagraph>
          </div>
        </div>
        <div>
          <div class="mb-2 flex gap-x-2 lg:mb-0">
            <BaseButton
              size="icon-sm"
              rounded="md"
              @click="emit('reply-clicked')"
            >
              <Icon name="solar:reply-2-linear" class="size-4" />
            </BaseButton>
            <BaseButton
              size="icon-sm"
              rounded="md"
              @click="emit('bookmark-clicked')"
            >
              <Icon name="solar:bookmark-linear" class="size-4" />
            </BaseButton>
            <BaseButton
              size="icon-sm"
              rounded="md"
              @click="emit('delete-clicked')"
            >
              <Icon name="solar:trash-bin-trash-linear" class="size-4" />
            </BaseButton>
          </div>
        </div>
      </div>

      <!-- Message body -->
      <div class="nui-slimscroll h-[calc(100vh_-_316px)] overflow-y-auto p-8">
        <div v-if="message?.attachments?.length" class="mb-3 flex items-center gap-2">
          <Icon name="solar:paperclip-linear" class="size-4 text-muted-500 dark:text-muted-400" />
          <span>&middot;</span>
          <BaseTag rounded="full">
            {{ message?.attachments.length }} attachment{{ message.attachments.length > 1 ? 's' : '' }}
          </BaseTag>
        </div>

        <BaseHeading size="2xl" weight="medium" class="text-muted-900 dark:text-white">
          {{ message?.title }}
        </BaseHeading>

        <article class="text-muted-500 dark:text-muted-400 mt-8 leading-7 tracking-wider">
          <p class="font-sans whitespace-pre-line">
            {{ message?.content }}
          </p>
        </article>

        <div v-if="message?.attachments?.length" class="mt-16 grid grid-cols-1 gap-4 sm:grid-cols-4">
          <button
            v-for="(file, index) in message?.attachments"
            :key="index"
            type="button"
            class="cursor-pointer hover:bg-muted-200/50 dark:hover:bg-muted-800/60 group flex items-center gap-3 rounded-xl p-2 text-start transition-colors duration-300"
            @click="downloadAttachment(file)"
          >
            <img
              :src="getAttachmentIcon(file.type)"
              :alt="getAttachmentAlt(file.type)"
              class="size-11"
            >
            <span class="block font-sans">
              <span class="text-muted-900 dark:text-muted-100 block text-sm font-semibold">
                {{ file.name }}
              </span>
              <span class="text-muted-600 dark:text-muted-400 block text-xs">
                {{ file.size }}
              </span>
            </span>
            <span class="ms-auto block">
              <span class="me-3 flex items-center justify-center opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                <Icon
                  name="lucide:arrow-down"
                  class="size-4"
                />
              </span>
            </span>
          </button>
        </div>
      </div>

      <!-- Message reply -->
      <TairoEmailReply
        @send-reply="(content) => emit('send-reply', content)"
        @attach-file="emit('attach-file')"
        @attach-image="emit('attach-image')"
      />
    </div>
  </div>
</template>
