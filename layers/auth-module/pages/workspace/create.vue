<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useFieldError, useForm } from 'vee-validate'
import { z } from 'zod'
import { RadioGroupRoot } from 'reka-ui'

definePageMeta({
  title: 'Create workspace',
  middleware: 'auth',
  preview: {
    title: 'Form layout 1',
    description: 'For forms and input fields',
    categories: ['layouts', 'forms'],
    src: '/img/screens/layouts-form-1.png',
    srcDark: '/img/screens/layouts-form-1-dark.png',
    order: 47,
  },
})

const { createWorkspace, getUserProfiles, user } = useAuth()
const router = useRouter()

// Load existing profiles
const existingProfiles = ref<Array<any>>([])
const loadingProfiles = ref(false)

onMounted(async () => {
  loadingProfiles.value = true
  try {
    existingProfiles.value = await getUserProfiles()
  }
  catch (error) {
    console.error('Failed to load profiles:', error)
  }
  finally {
    loadingProfiles.value = false
  }
})

// This is the object that will contain the validation messages
const VALIDATION_TEXT = {
  NAME_REQUIRED: 'Workspace name can\'t be empty',
  EMAIL_REQUIRED: 'Email address can\'t be empty',
  TYPE_REQUIRED: 'Please select a workspace type',
  PROFILE_TYPE_REQUIRED: 'Please select a profile option',
  AVATAR_TOO_BIG: `Avatar size must be less than 1MB`,
}

// This is the Zod schema for the form input
const zodSchema = z
  .object({
    avatar: z.custom<File>(v => v instanceof File).nullable(),
    workspace: z.object({
      name: z.string().min(1, VALIDATION_TEXT.NAME_REQUIRED),
      email: z.string().min(1, VALIDATION_TEXT.EMAIL_REQUIRED).email('Please enter a valid email'),
      type: z.string().min(1, VALIDATION_TEXT.TYPE_REQUIRED),
      website: z.string().optional(),
      description: z.string().optional(),
    }),
    profile: z.object({
      createNew: z.string(),
      selectedProfileId: z.string().optional(),
      displayName: z.string().optional(),
      bio: z.string().optional(),
    }),
  })
  .superRefine((data, ctx) => {
    // Custom validation for avatar size
    if (data.avatar && data.avatar.size > 1000000) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: VALIDATION_TEXT.AVATAR_TOO_BIG,
        path: ['avatar'],
      })
    }
  })

type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  avatar: null,
  workspace: {
    name: '',
    email: user.value?.email || '',
    website: '',
    type: '',
    description: '',
  },
  profile: {
    createNew: 'true',
    selectedProfileId: '',
    displayName: user.value?.username || '',
    bio: '',
  },
} satisfies FormInput

// This is the computed value that will be used to display the current avatar
const currentAvatar = computed(() => '/img/avatars/workspace.svg')

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
  meta,
  values,
  errors,
  resetForm,
  setFieldValue,
  setErrors,
} = useForm({
  validationSchema,
  initialValues,
})

const success = ref(false)
const fieldsWithErrors = computed(() => Object.keys(errors.value).length)

// Handle file input
const inputFile = ref<FileList | null>(null)
const fileError = useFieldError('avatar')
watch(inputFile, (value) => {
  const file = value?.item(0) || null
  setFieldValue('avatar', file)
})

// Ask the user for confirmation before leaving the page if the form has unsaved changes
onBeforeRouteLeave(() => {
  if (meta.value.dirty) {
    return confirm('You have unsaved changes. Are you sure you want to leave?')
  }
})

const toaster = useNuiToasts()

// This is where you would send the form data to the server
const onSubmit = handleSubmit(
  async (values) => {
    success.value = false

    try {
      // Create the workspace
      const result = await createWorkspace({
        name: values.workspace.name,
        description: values.workspace.description || undefined,
        type: values.workspace.type,
        useExistingProfile: values.profile.createNew === 'false',
        existingProfileId: values.profile.selectedProfileId || undefined,
        newProfileData: values.profile.createNew === 'true'
          ? {
              displayName: values.profile.displayName || user.value?.username || '',
              bio: values.profile.bio || undefined,
            }
          : undefined,
      })

      toaster.add({
        title: 'Success',
        description: `Workspace has been created!`,
        icon: 'ph:check',
        progress: true,
      })

      // Reset form and redirect
      resetForm()

      setTimeout(() => {
        router.push('/user/workspaces')
      }, 1000)

      success.value = true
    }
    catch (error: any) {
      // Handle errors
      if (error.message) {
        toaster.add({
          title: 'Oops!',
          description: error.message,
          icon: 'lucide:alert-triangle',
          progress: true,
        })
      }
    }
  },
  (_error) => {
    // this callback is optional and called only if the form has errors
    success.value = false

    // you can use it to scroll to the first error
    document.documentElement.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  },
)
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <form
      method="POST"
      action=""
      class="flex flex-col"
      novalidate
      @submit.prevent="onSubmit"
    >
      <div class="mt-12">
        <div class="flex w-full max-w-5xl flex-col space-y-16">
          <!-- Dual column -->
          <div class="grid grid-cols-12 gap-8">
            <!-- Label column -->
            <div class="col-span-12 md:col-span-4">
              <div class="max-w-xs pe-4 space-y-2">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  <span>Workspace avatar</span>
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Upload a workspace logo or avatar. This image will be displayed in the workspace profile.
                </BaseParagraph>
              </div>
            </div>
            <!-- Input column -->
            <div class="col-span-12 md:col-span-8">
              <div
                class="relative mb-5 flex flex-col gap-4"
              >
                <TairoFullscreenDropfile
                  icon="solar:gallery-linear"
                  :filter-file-dropped="
                    (file) => file.type.startsWith('image')
                  "
                  @drop="
                    (value) => {
                      inputFile = value
                    }
                  "
                />
                <TairoInputFileHeadless
                  v-slot="{ open, remove, preview, files }"
                  v-model="inputFile"
                  accept="image/*"
                >
                  <div class="relative size-20">
                    <img
                      v-if="files?.length && files.item(0)"
                      :src="preview(files.item(0)!).value"
                      alt="Upload preview"
                      class="bg-muted-200 dark:bg-muted-700/60 size-20 rounded-full object-cover object-center"
                    >
                    <img
                      v-else
                      :src="currentAvatar"
                      alt="Upload preview"
                      class="bg-muted-200 dark:bg-muted-700/60 size-20 rounded-full object-cover object-center dark:invert"
                    >
                    <div
                      v-if="files?.length && files.item(0)"
                      class="absolute bottom-0 end-0 z-20"
                    >
                      <BaseTooltip content="Remove image">
                        <BaseButton
                          size="icon-sm"
                          rounded="full"
                          class="scale-90"
                          @click="remove(files.item(0)!)"
                        >
                          <Icon name="lucide:x" class="size-4" />
                        </BaseButton>
                      </BaseTooltip>
                    </div>
                    <div v-else class="absolute bottom-0 end-0 z-20">
                      <BaseTooltip content="Upload image">
                        <BaseButton
                          size="icon-sm"
                          rounded="full"
                          @click="open"
                        >
                          <Icon name="lucide:plus" class="size-4" />
                        </BaseButton>
                      </BaseTooltip>
                    </div>
                  </div>
                </TairoInputFileHeadless>
                <div
                  v-if="fileError"
                  class="text-destructive-600 inline-block font-sans text-[.8rem]"
                >
                  {{ fileError }}
                </div>
              </div>
            </div>
          </div>
          <!-- Dual column -->
          <div class="grid grid-cols-12 gap-8">
            <!-- Label column -->
            <div class="col-span-12 md:col-span-4">
              <div class="max-w-xs pe-4 space-y-2">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  <span>Workspace info</span>
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Fill in basic information about the workspace. This information will be displayed in the workspace profile.
                </BaseParagraph>
              </div>
            </div>
            <!-- Input column -->
            <div class="col-span-12 md:col-span-8">
              <div class="grid grid-cols-12 gap-4">
                <div class="col-span-12 lg:col-span-6">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="workspace.name"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Workspace Name"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                      required
                    >
                      <BaseInput
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        :model-value="field.value"
                        placeholder="Ex: Acme Inc."
                        type="text"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>
                <div class="col-span-12 lg:col-span-6">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="workspace.email"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Contact Email"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                      required
                    >
                      <BaseInput
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        :model-value="field.value"
                        placeholder="Ex: <EMAIL>"
                        type="email"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>
                <div class="col-span-12">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="workspace.website"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Workspace website"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                    >
                      <TairoInput
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        :model-value="field.value"
                        placeholder="Ex: https://acme.co"
                        icon="lucide:globe"
                        type="text"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>
                <div class="col-span-12">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="workspace.description"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Workspace description"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                    >
                      <BaseTextarea
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        placeholder="Describe your workspace..."
                        :model-value="field.value"
                        rows="4"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>
              </div>
            </div>
          </div>
          <!-- Dual column -->
          <div class="grid grid-cols-12 gap-8">
            <!-- Label column -->
            <div class="col-span-12 md:col-span-4">
              <div class="max-w-xs pe-4 space-y-2">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  <span>Workspace type</span>
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Select the type of workspace you're creating. This helps us provide the right features for your needs.
                </BaseParagraph>
              </div>
            </div>
            <!-- Input column -->
            <div class="col-span-12 md:col-span-8">
              <div class="mb-6">
                <Field
                  v-slot="{ field, errorMessage, handleChange }"
                  name="workspace.type"
                >
                  <BaseField
                    label="Workspace type"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    required
                  >
                    <RadioGroupRoot
                      :model-value="field.value"
                      class="grid gap-4 lg:grid-cols-3"
                      @update:model-value="handleChange"
                    >
                      <TairoRadioCard
                        value="personal"
                        icon="solar:home-2-linear"
                        label="Personal"
                        subtitle="For individual use"
                        variant="muted"
                      />
                      <TairoRadioCard
                        value="team"
                        icon="solar:users-group-rounded-linear"
                        label="Team"
                        subtitle="For collaboration"
                        variant="muted"
                      />
                      <TairoRadioCard
                        value="enterprise"
                        icon="solar:buildings-linear"
                        label="Enterprise"
                        subtitle="For organizations"
                        variant="muted"
                      />
                    </RadioGroupRoot>
                  </BaseField>
                </Field>
              </div>
            </div>
          </div>
          <!-- Dual column -->
          <div class="grid grid-cols-12 gap-8">
            <!-- Label column -->
            <div class="col-span-12 md:col-span-4">
              <div class="max-w-xs pe-4 space-y-2">
                <BaseHeading
                  as="h3"
                  size="md"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  <span>Your profile</span>
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                  Each workspace can have its own profile. Choose how you want to set up your profile for this workspace.
                </BaseParagraph>
              </div>
            </div>
            <!-- Input column -->
            <div class="col-span-12 md:col-span-8">
              <div class="space-y-6">
                <!-- Profile Option -->
                <Field
                  v-slot="{ field, errorMessage, handleChange }"
                  name="profile.createNew"
                >
                  <BaseField
                    label="Profile setup"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                  >
                    <BaseRadioGroup
                      :model-value="field.value"
                      @update:model-value="handleChange"
                    >
                      <BaseRadio
                        value="true"
                        label="Create a new profile"
                        sublabel="Start fresh with a profile specific to this workspace"
                      />
                      <BaseRadio
                        value="false"
                        label="Use an existing profile"
                        sublabel="Copy profile information from another workspace"
                        :disabled="existingProfiles.length === 0"
                      />
                    </BaseRadioGroup>
                  </BaseField>
                </Field>

                <!-- New Profile Fields -->
                <div v-if="values.profile.createNew === 'true'" class="grid grid-cols-12 gap-4">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="profile.displayName"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Display Name"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                      class="col-span-12 lg:col-span-6"
                    >
                      <BaseInput
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        :model-value="field.value"
                        placeholder="Your name in this workspace"
                        type="text"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>

                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="profile.bio"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Bio"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                      class="col-span-12"
                    >
                      <BaseTextarea
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        placeholder="Tell us about yourself in this workspace..."
                        :model-value="field.value"
                        rows="3"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>

                <!-- Existing Profile Selection -->
                <div v-else>
                  <div v-if="loadingProfiles" class="flex justify-center py-8">
                    <BaseLoader />
                  </div>
                  <div v-else-if="existingProfiles.length === 0" class="p-4 bg-muted-100 dark:bg-muted-800 rounded-lg">
                    <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
                      You don't have any profiles in other workspaces yet. Create a new profile for this workspace.
                    </BaseParagraph>
                  </div>
                  <div v-else>
                    <Field
                      v-slot="{ field, errorMessage, handleChange }"
                      name="profile.selectedProfileId"
                    >
                      <BaseField
                        label="Select a profile to copy"
                        :state="errorMessage ? 'error' : 'idle'"
                        :error="errorMessage"
                        :disabled="isSubmitting"
                      >
                        <RadioGroupRoot
                          :model-value="field.value"
                          class="grid gap-4 lg:grid-cols-2"
                          @update:model-value="handleChange"
                        >
                          <div
                            v-for="profile in existingProfiles"
                            :key="profile.id"
                            class="relative"
                          >
                            <TairoRadioCard
                              :value="profile.id"
                              :label="profile.displayName"
                              :subtitle="`From ${profile.workspaceName}`"
                              variant="muted"
                            >
                              <template #header>
                                <div class="flex items-center gap-3">
                                  <BaseAvatar
                                    v-if="profile.avatarUrl"
                                    :src="profile.avatarUrl"
                                    size="sm"
                                  />
                                  <BaseAvatar
                                    v-else
                                    :text="profile.displayName?.charAt(0) || '?'"
                                    size="sm"
                                    class="bg-primary-500/10 text-primary-500"
                                  />
                                  <div>
                                    <BaseText weight="medium">
                                      {{ profile.displayName }}
                                    </BaseText>
                                    <BaseText size="xs" class="text-muted-400">
                                      {{ profile.workspaceName }}
                                    </BaseText>
                                  </div>
                                </div>
                              </template>
                              <BaseParagraph v-if="profile.bio" size="xs" class="text-muted-400 mt-2">
                                {{ profile.bio }}
                              </BaseParagraph>
                            </TairoRadioCard>
                          </div>
                        </RadioGroupRoot>
                      </BaseField>
                    </Field>
                  </div>
                </div>
              </div>

              <div
                class="mt-8 flex flex-col md:flex-row md:justify-end gap-3"
              >
                <BaseButton
                  to="/"
                  variant="ghost"
                  rounded="md"
                  class="w-full sm:w-32"
                >
                  Cancel
                </BaseButton>
                <BaseButton
                  type="submit"
                  variant="primary"
                  rounded="md"
                  class="w-full sm:w-32"
                  :loading="isSubmitting"
                  :disabled="isSubmitting"
                >
                  Create
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>
