<script setup lang="ts">
import type { EmailMessage } from '~/types/integration'
import TairoEmailSidebar from '~/components/email/TairoEmailSidebar.vue'

definePageMeta({
  title: 'Inbox',
  preview: {
    title: 'Inbox app',
    description: 'For email and messaging apps',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-inbox.png',
    srcDark: '/img/screens/dashboards-inbox-dark.png',
    order: 25,
  },
})

// Composables
const { emailAccounts, loading: accountsLoading, syncEmailAccount } = useEmailAccounts()
const toaster = useToast()
const { md } = useTailwindBreakpoints()

// State
const search = ref('')
const activeAccountId = ref<string | null>(null)
const selectedEmailId = ref<string | null>(null)
const showReplyModal = ref(false)
const replyToEmail = ref<EmailMessage | null>(null)
const sidebarOpen = ref(true)
const filter = ref('all') // 'all', 'unread', 'read', 'starred'

// Get active account
const activeAccount = computed(() =>
  emailAccounts.value.find(account => account.id === activeAccountId.value),
)

// Email management for active account
const {
  emails,
  loading: emailsLoading,
  syncing,
  unreadCount,
  markEmailAsRead,
  toggleEmailStar,
  deleteEmail,
  sendReply,
  syncEmails,
} = useEmails(activeAccountId)

// Selected email
const selectedEmail = computed(() =>
  emails.value.find(email => email.id === selectedEmailId.value),
)

// Filtered emails based on current filter
const filteredEmails = computed(() => {
  let filtered = emails.value

  switch (filter.value) {
    case 'unread':
      filtered = filtered.filter(email => !email.flags?.seen)
      break
    case 'read':
      filtered = filtered.filter(email => email.flags?.seen)
      break
    case 'starred':
      filtered = filtered.filter(email => email.flags?.flagged)
      break
    // 'all' shows everything
  }

  // Apply search filter
  if (search.value.trim()) {
    const searchTerm = search.value.toLowerCase()
    filtered = filtered.filter(email =>
      email.subject.toLowerCase().includes(searchTerm)
      || (email.from.name || '').toLowerCase().includes(searchTerm)
      || email.from.email.toLowerCase().includes(searchTerm),
    )
  }

  return filtered
})

// Set initial active account when accounts load
watch(emailAccounts, (accounts) => {
  if (accounts.length > 0 && !activeAccountId.value) {
    // Find the default account or use the first one
    const defaultAccount = accounts.find(acc => acc.isDefault) || accounts[0]
    activeAccountId.value = defaultAccount.id
  }
}, { immediate: true })

// Update emails when active account changes
watch(activeAccountId, (newAccountId) => {
  selectedEmailId.value = null

  if (newAccountId) {
    // The useEmails composable will automatically update when accountId changes
  }
})

// Handlers
function handleAccountSelected(accountId: string | Event) {
  if (typeof accountId === 'string') {
    activeAccountId.value = accountId
  }
  else {
    // Handle select event
    const target = accountId.target as HTMLSelectElement
    activeAccountId.value = target.value
  }
}

const showIntegrationModal = ref(false)

function handleAddAccount() {
  navigateTo('/user/integrations')
}

function handleAddIntegrationModal() {
  showIntegrationModal.value = true
}

function handleManageIntegrationsFromSidebar() {
  navigateTo('/user/integrations')
}

async function handleSyncAccount(accountId: string) {
  try {
    await syncEmailAccount(accountId)
    toaster.show({
      title: 'Sync completed',
      message: 'Emails have been synchronized successfully',
      color: 'success',
      icon: 'lucide:check-circle',
      closable: true,
    })
  }
  catch (error) {
    toaster.show({
      title: 'Sync failed',
      message: 'Failed to synchronize emails. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
      closable: true,
    })
  }
}

function handleManageIntegrations() {
  navigateTo('/user/integrations')
}

function selectEmail(email: EmailMessage) {
  selectedEmailId.value = email.id

  // Mark as read when selected
  if (!email.flags?.seen) {
    markEmailAsRead(email.id, true).catch(console.error)
  }
}

function setFilter(newFilter: string) {
  filter.value = newFilter
  selectedEmailId.value = null
}

function formatDate(dateStr: string) {
  const date = new Date(dateStr)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

  if (diffHours < 1)
    return 'now'
  if (diffHours < 24)
    return `${diffHours}h`
  if (diffHours < 168)
    return `${Math.floor(diffHours / 24)}d`
  return date.toLocaleDateString()
}

function getProviderIcon(provider: string) {
  const icons = {
    gmail: 'logos:google-gmail',
    outlook: 'simple-icons:microsoftoutlook',
    yahoo: 'simple-icons:yahoo',
    imap: 'lucide:mail',
    exchange: 'simple-icons:microsoftexchange',
    custom: 'lucide:server',
  }
  return icons[provider as keyof typeof icons] || 'lucide:mail'
}

function handleReplyClicked(email: EmailMessage) {
  replyToEmail.value = email
  showReplyModal.value = true
}

async function handleStarClicked(email: EmailMessage) {
  try {
    await toggleEmailStar(email.id)
  }
  catch (error) {
    toaster.show({
      title: 'Error',
      message: 'Failed to update email star status',
      color: 'danger',
      icon: 'lucide:alert-circle',
      closable: true,
    })
  }
}

async function handleDeleteClicked(email: EmailMessage) {
  try {
    await deleteEmail(email.id)

    if (selectedEmailId.value === email.id) {
      selectedEmailId.value = null
    }

    toaster.show({
      title: 'Email deleted',
      message: 'The email has been deleted successfully',
      color: 'success',
      icon: 'lucide:trash-2',
      closable: true,
    })
  }
  catch (error) {
    toaster.show({
      title: 'Delete failed',
      message: 'Failed to delete email. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
      closable: true,
    })
  }
}

async function handleSendReply(replyData: any) {
  if (!replyToEmail.value)
    return

  try {
    await sendReply(replyToEmail.value.id, replyData)
    showReplyModal.value = false
    replyToEmail.value = null

    toaster.show({
      title: 'Reply sent',
      message: 'Your reply has been sent successfully',
      color: 'success',
      icon: 'lucide:send',
      closable: true,
    })
  }
  catch (error) {
    toaster.show({
      title: 'Send failed',
      message: 'Failed to send reply. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
      closable: true,
    })
  }
}
</script>

<template>
  <div class="-mt-6 w-full overflow-hidden dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="h-[calc(100dvh_-_56px)] flex">
      <!-- Email Sidebar -->
      <div
        class="w-80 border-r border-muted-200 dark:border-muted-700"
        :class="{ hidden: !sidebarOpen && md }"
      >
        <TairoEmailSidebar
          :accounts="emailAccounts"
          :active-account-id="activeAccountId"
          :loading="accountsLoading"
          :search="search"
          :filter="filter"
          :emails="emails"
          :unread-count="unreadCount"
          @account-selected="handleAccountSelected"
          @add-account="handleAddIntegrationModal"
          @sync-account="handleSyncAccount"
          @manage-integrations="handleManageIntegrationsFromSidebar"
          @search-updated="search = $event"
          @filter-updated="setFilter"
        />
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col">
        <!-- Toolbar -->
        <div class="p-4 border-b border-muted-200 dark:border-muted-700 bg-white dark:bg-muted-900">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <BaseButton
                v-if="!md"
                size="sm"
                variant="ghost"
                @click="sidebarOpen = !sidebarOpen"
              >
                <Icon name="lucide:menu" class="size-4" />
              </BaseButton>
              <div v-if="activeAccount" class="flex items-center gap-3">
                <Icon :name="getProviderIcon(activeAccount.provider)" class="size-5" />
                <div>
                  <div class="text-sm font-medium">
                    {{ activeAccount.displayName }}
                  </div>
                  <div class="text-xs text-muted-500">
                    {{ filteredEmails.length }} emails
                  </div>
                </div>
              </div>
              <div v-else class="text-sm text-muted-500">
                Select an email account to view messages
              </div>
            </div>

            <div class="flex items-center gap-2">
              <BaseButton
                v-if="activeAccount"
                size="sm"
                variant="ghost"
                :loading="syncing"
                @click="handleSyncAccount(activeAccount.id)"
              >
                <Icon name="lucide:refresh-cw" class="size-4" :class="{ 'animate-spin': syncing }" />
              </BaseButton>
            </div>
          </div>
        </div>

        <!-- Email List -->
        <div class="flex-1 flex">
          <!-- Email List Panel -->
          <div class="w-80 border-r border-muted-200 dark:border-muted-700 bg-white dark:bg-muted-900 flex flex-col">
            <!-- Loading State -->
            <div v-if="emailsLoading" class="p-4 space-y-3">
              <div v-for="i in 5" :key="i" class="animate-pulse">
                <div class="h-16 bg-muted-200 dark:bg-muted-800 rounded-lg" />
              </div>
            </div>

            <!-- No Emails State -->
            <div v-else-if="!activeAccount" class="flex-1 flex items-center justify-center p-8">
              <div class="text-center">
                <Icon name="lucide:mail" class="mx-auto size-12 text-muted-400 dark:text-muted-600 mb-4" />
                <BaseHeading size="sm" class="text-muted-600 dark:text-muted-400 mb-2">
                  Select Email Account
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
                  Choose an email account from the sidebar to view your messages
                </BaseParagraph>
              </div>
            </div>

            <!-- Empty Email List -->
            <div v-else-if="filteredEmails.length === 0" class="flex-1 flex items-center justify-center p-8">
              <div class="text-center">
                <Icon name="lucide:inbox" class="mx-auto size-12 text-muted-400 dark:text-muted-600 mb-4" />
                <BaseHeading size="sm" class="text-muted-600 dark:text-muted-400 mb-2">
                  No Emails Found
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
                  {{ search ? 'No emails match your search' : 'Your inbox is empty' }}
                </BaseParagraph>
              </div>
            </div>

            <!-- Email Items -->
            <div v-else class="flex-1 overflow-y-auto">
              <div
                v-for="email in filteredEmails"
                :key="email.id"
                class="p-4 border-b border-muted-200 dark:border-muted-700 cursor-pointer transition-colors hover:bg-muted-50 dark:hover:bg-muted-800/50"
                :class="{
                  'bg-primary-50 dark:bg-primary-900/10': selectedEmailId === email.id,
                  'font-semibold': !email.flags?.seen,
                }"
                @click="selectEmail(email)"
              >
                <div class="flex items-start gap-3">
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between mb-1">
                      <div class="text-sm font-medium text-muted-900 dark:text-muted-100 truncate">
                        {{ email.from.name || email.from.email }}
                      </div>
                      <div class="flex items-center gap-2">
                        <button
                          class="p-1 hover:bg-muted-200 dark:hover:bg-muted-700 rounded transition-colors"
                          @click.stop="handleStarClicked(email)"
                        >
                          <Icon
                            :name="email.flags?.flagged ? 'lucide:star' : 'lucide:star'"
                            class="size-3"
                            :class="email.flags?.flagged ? 'text-yellow-500' : 'text-muted-400'"
                          />
                        </button>
                        <span class="text-xs text-muted-500">{{ formatDate(email.date) }}</span>
                      </div>
                    </div>
                    <div class="text-sm text-muted-700 dark:text-muted-300 truncate mb-1">
                      {{ email.subject }}
                    </div>
                    <div class="text-xs text-muted-500 dark:text-muted-400 truncate">
                      {{ email.body?.text?.substring(0, 100) }}...
                    </div>
                  </div>
                  <div v-if="!email.flags?.seen" class="w-2 h-2 bg-primary-500 rounded-full mt-1" />
                </div>
              </div>
            </div>
          </div>

          <!-- Email Detail Panel -->
          <div class="flex-1 bg-white dark:bg-muted-900 flex flex-col">
            <!-- No Email Selected -->
            <div v-if="!selectedEmail" class="flex-1 flex items-center justify-center p-8">
              <div class="text-center">
                <Icon name="lucide:mail-open" class="mx-auto size-12 text-muted-400 dark:text-muted-600 mb-4" />
                <BaseHeading size="sm" class="text-muted-600 dark:text-muted-400 mb-2">
                  Select Email
                </BaseHeading>
                <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
                  Choose an email from the list to read its contents
                </BaseParagraph>
              </div>
            </div>

            <!-- Email Content -->
            <div v-else class="flex-1 flex flex-col">
              <!-- Email Header -->
              <div class="p-6 border-b border-muted-200 dark:border-muted-700">
                <div class="flex items-start justify-between mb-4">
                  <div class="flex-1">
                    <BaseHeading size="lg" class="mb-2">
                      {{ selectedEmail.subject }}
                    </BaseHeading>
                    <div class="flex items-center gap-4 text-sm text-muted-600 dark:text-muted-400">
                      <div class="flex items-center gap-2">
                        <span class="font-medium">From:</span>
                        <span>{{ selectedEmail.from.name || selectedEmail.from.email }}</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <span class="font-medium">Date:</span>
                        <span>{{ new Date(selectedEmail.date).toLocaleString() }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center gap-2">
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleStarClicked(selectedEmail)"
                    >
                      <Icon
                        :name="selectedEmail.flags?.flagged ? 'lucide:star' : 'lucide:star'"
                        class="size-4"
                        :class="selectedEmail.flags?.flagged ? 'text-yellow-500' : 'text-muted-400'"
                      />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleReplyClicked(selectedEmail)"
                    >
                      <Icon name="lucide:reply" class="size-4" />
                      Reply
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleDeleteClicked(selectedEmail)"
                    >
                      <Icon name="lucide:trash-2" class="size-4" />
                    </BaseButton>
                  </div>
                </div>
              </div>

              <!-- Email Body -->
              <div class="flex-1 p-6 overflow-y-auto">
                <div v-if="selectedEmail.body?.html" class="prose dark:prose-invert max-w-none" v-html="selectedEmail.body.html" />
                <div v-else class="whitespace-pre-wrap text-muted-700 dark:text-muted-300">
                  {{ selectedEmail.body?.text }}
                </div>

                <!-- Attachments -->
                <div v-if="selectedEmail.attachments?.length > 0" class="mt-6 pt-6 border-t border-muted-200 dark:border-muted-700">
                  <BaseHeading size="sm" class="mb-3">
                    Attachments
                  </BaseHeading>
                  <div class="space-y-2">
                    <div
                      v-for="attachment in selectedEmail.attachments"
                      :key="attachment.id"
                      class="flex items-center gap-3 p-3 bg-muted-50 dark:bg-muted-800 rounded-lg"
                    >
                      <Icon name="lucide:paperclip" class="size-4 text-muted-500" />
                      <div class="flex-1">
                        <div class="text-sm font-medium">
                          {{ attachment.filename }}
                        </div>
                        <div class="text-xs text-muted-500">
                          {{ (attachment.size / 1024).toFixed(1) }} KB
                        </div>
                      </div>
                      <BaseButton size="xs" variant="ghost">
                        <Icon name="lucide:download" class="size-3" />
                      </BaseButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reply Modal -->
    <TairoEmailReply
      v-if="showReplyModal"
      v-model:visible="showReplyModal"
      :original-email="replyToEmail"
      :account="activeAccount"
      @send="handleSendReply"
      @cancel="showReplyModal = false"
    />

    <!-- Integration Modal -->
    <TairoModal
      :open="showIntegrationModal"
      size="lg"
      @close="showIntegrationModal = false"
    >
      <template #header>
        <BaseHeading size="lg">
          Add Email Integration
        </BaseHeading>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Gmail Integration -->
          <div class="p-4 border border-muted-200 dark:border-muted-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors">
            <div class="flex items-center gap-3 mb-2">
              <Icon name="logos:google-gmail" class="size-6" />
              <div>
                <div class="font-medium">
                  Gmail
                </div>
                <div class="text-xs text-muted-500">
                  Connect your Gmail account
                </div>
              </div>
            </div>
          </div>

          <!-- Outlook Integration -->
          <div class="p-4 border border-muted-200 dark:border-muted-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors">
            <div class="flex items-center gap-3 mb-2">
              <Icon name="logos:microsoft-outlook" class="size-6" />
              <div>
                <div class="font-medium">
                  Outlook
                </div>
                <div class="text-xs text-muted-500">
                  Connect your Outlook account
                </div>
              </div>
            </div>
          </div>

          <!-- Yahoo Integration -->
          <div class="p-4 border border-muted-200 dark:border-muted-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors">
            <div class="flex items-center gap-3 mb-2">
              <Icon name="simple-icons:yahoo" class="size-6" />
              <div>
                <div class="font-medium">
                  Yahoo Mail
                </div>
                <div class="text-xs text-muted-500">
                  Connect your Yahoo account
                </div>
              </div>
            </div>
          </div>

          <!-- IMAP Integration -->
          <div class="p-4 border border-muted-200 dark:border-muted-700 rounded-lg hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors">
            <div class="flex items-center gap-3 mb-2">
              <Icon name="lucide:mail" class="size-6" />
              <div>
                <div class="font-medium">
                  IMAP/SMTP
                </div>
                <div class="text-xs text-muted-500">
                  Custom email server
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="pt-4 border-t border-muted-200 dark:border-muted-700">
          <BaseButton
            class="w-full"
            @click="handleManageIntegrationsFromSidebar"
          >
            <Icon name="lucide:settings" class="size-4" />
            Advanced Integration Settings
          </BaseButton>
        </div>
      </div>

      <template #footer>
        <BaseButton variant="outline" @click="showIntegrationModal = false">
          Close
        </BaseButton>
      </template>
    </TairoModal>
  </div>
</template>
