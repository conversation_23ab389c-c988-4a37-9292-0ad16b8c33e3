<script setup lang="ts">
definePageMeta({
  title: 'Integrations',
  preview: {
    title: 'Preferences - Integrations',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-integrations.png',
    srcDark: '/img/screens/layouts-preferences-integrations-dark.png',
    order: 87,
    new: true,
  },
})

// Initialize API and toaster
const integrationsApi = useIntegrationsApi()
const toaster = useToast()

// Google Calendar integration
const {
  isConnecting: isGoogleCalendarConnecting,
  isConnected: isGoogleCalendarConnected,
  connectGoogleCalendar,
  disconnectGoogleCalendar,
  syncFromGoogle,
  syncToGoogle,
  currentIntegration: googleCalendarIntegration,
  syncStatus: googleCalendarSyncStatus,
} = useGoogleCalendar()

const integrations = [
  {
    name: 'Mailchimp',
    description: 'Lorem ipsum dolor sit amet, consectetur adipis.',
    icon: 'logos:mailchimp-freddie',
  },
  {
    name: 'Zapier',
    description: 'Connect with Zapier API.',
    icon: 'logos:zapier-icon',
    live: true,
  },
  {
    name: 'Telegram',
    description: 'Lorem ipsum dolor sit amet.',
    icon: 'logos:telegram',
  },
  {
    name: 'Slack',
    description: 'Lorem ipsum dolor sit amet, consectetur adipis.',
    icon: 'logos:slack-icon',
  },
  {
    name: 'Dropbox',
    description: 'Lorem ipsum dolor sit amet adipis.',
    icon: 'logos:dropbox',
  },
  {
    name: 'OpenAI',
    description: 'Connect to OpenAI API.',
    icon: 'logos:openai',
    live: true,
  },
  {
    name: 'Grok',
    description: 'Connect to Grok AI API.',
    icon: 'logos:grok-ai',
    live: true,
  },
  {
    name: 'Claude',
    description: 'Connect to Claude AI API.',
    icon: 'logos:claude-ai',
    live: true,
  },
  {
    name: 'Gemini',
    description: 'Connect to Google Gemini AI API.',
    icon: 'logos:google-gemini',
    live: true,
  },
  {
    name: 'Ollama',
    description: 'Connect to local Ollama instance for AI development.',
    icon: 'simple-icons:ollama',
    live: true,
  },
  {
    name: 'Stripe',
    description: 'Connect to Stripe for payment processing.',
    icon: 'logos:stripe-icon',
    live: true,
  },
  {
    name: 'Paypal',
    description: 'Connect to Paypal for payment processing.',
    icon: 'logos:paypal-icon',
    live: true,
  },
  // Social Media Integrations
  {
    name: 'Facebook',
    description: 'Connect your Facebook account to manage pages and posts.',
    icon: 'logos:facebook',
    live: true,
    category: 'social',
  },
  {
    name: 'Instagram',
    description: 'Connect your Instagram Business account.',
    icon: 'logos:instagram-icon',
    live: true,
    category: 'social',
  },
  {
    name: 'LinkedIn',
    description: 'Connect your LinkedIn profile or company page.',
    icon: 'logos:linkedin-icon',
    live: true,
    category: 'social',
  },
  {
    name: 'Twitter',
    description: 'Connect your X account to post tweets and threads.',
    icon: 'logos:twitter',
    live: true,
    category: 'social',
  },
  {
    name: 'Reddit',
    description: 'Connect your Reddit account to post and manage content.',
    icon: 'logos:reddit-icon',
    live: true,
    category: 'social',
  },
  {
    name: 'Pinterest',
    description: 'Connect your Pinterest account to manage pins and boards.',
    icon: 'logos:pinterest',
    live: true,
    category: 'social',
  },
  {
    name: 'TikTok',
    description: 'Connect your TikTok account to manage videos.',
    icon: 'logos:tiktok-icon',
    live: true,
    category: 'social',
  },
  {
    name: 'YouTube',
    description: 'Connect your YouTube channel to manage videos and playlists.',
    icon: 'logos:youtube-icon',
    live: true,
    category: 'social',
  },
  // Email Integrations
  {
    name: 'Gmail',
    description: 'Connect your Gmail account for email management and automation.',
    icon: 'logos:google-gmail',
    live: true,
    category: 'email',
  },
  {
    name: 'Outlook',
    description: 'Connect your Outlook/Hotmail account for email sync.',
    icon: 'simple-icons:microsoftoutlook',
    live: true,
    category: 'email',
  },
  {
    name: 'Yahoo Mail',
    description: 'Connect your Yahoo Mail account using app passwords.',
    icon: 'logos:yahoo',
    live: true,
    category: 'email',
  },
  {
    name: 'Exchange Server',
    description: 'Connect to Microsoft Exchange Server for enterprise email.',
    icon: 'logos:microsoft-exchange',
    live: true,
    category: 'email',
  },
  {
    name: 'IMAP Server',
    description: 'Connect to any IMAP/SMTP server with custom settings.',
    icon: 'lucide:mail',
    live: true,
    category: 'email',
  },
  {
    name: 'Custom Email',
    description: 'Configure a custom email server with advanced settings.',
    icon: 'lucide:server',
    live: true,
    category: 'email',
  },
  // Calendar Integrations
  {
    name: 'Google Calendar',
    description: 'Sync your Google Calendar events with your workspace calendar.',
    icon: 'logos:google-calendar',
    live: true,
    category: 'calendar',
  },
  {
    name: 'Outlook Calendar',
    description: 'Connect your Outlook calendar for seamless event management.',
    icon: 'simple-icons:microsoftoutlook',
    category: 'calendar',
  },
  {
    name: 'Apple Calendar',
    description: 'Sync events with your Apple Calendar (iCloud).',
    icon: 'logos:apple',
    category: 'calendar',
  },
]

// Reactive data for modal and API keys
const modalVisible = ref({})
const editModalVisible = ref({})

// Email integration modal state
const emailModalVisible = ref(false)
const currentEmailProvider = ref('')
const confirmDisconnectModal = ref({ visible: false, integrationName: '', deleteType: 'soft' })

// Email account management
const { emailAccounts, loading: emailAccountsLoading } = useEmailAccounts()
const apiKeys = ref({})
const editApiKeys = ref({})
const editSettings = ref({})
const connectedIntegrations = ref([])
const savedIntegrations = ref([])
const isLoading = ref(false)
const isSaving = ref({})
const isUpdating = ref({})
const isToggling = ref({})
const isAuthenticating = ref(false)

// Integration method selection
const claudeIntegrationMethod = ref('api')
const geminiIntegrationMethod = ref('api')
const ollamaUrl = ref('http://localhost:11434')
const ollamaModels = ref([])
const isLoadingOllamaModels = ref(false)
const perplexityMcpConfig = ref('api')
const isValidatingApiKey = ref({})
const apiKeyValidation = ref({})

// Load existing integrations on mount
async function loadIntegrations() {
  try {
    isLoading.value = true
    const integrations = await integrationsApi.getAll()
    savedIntegrations.value = integrations

    // Populate connected integrations list
    connectedIntegrations.value = integrations
      .filter(integration => integration.isActive)
      .map(integration => integration.name)
  }
  catch (error) {
    console.error('Failed to load integrations:', error)
  }
  finally {
    isLoading.value = false
  }
}

// Call on mount
onMounted(() => {
  loadIntegrations()
})

// Email integration methods
function showEmailModal(provider) {
  currentEmailProvider.value = provider
  emailModalVisible.value = true
}

function closeEmailModal() {
  emailModalVisible.value = false
  currentEmailProvider.value = ''
}

async function handleEmailIntegrationSave(integration) {
  try {
    // The integration will be saved through the TairoEmailIntegrationModal
    // and the email accounts will be updated automatically via the composable
    emailModalVisible.value = false
    currentEmailProvider.value = ''

    // Reload integrations to reflect the new email account
    await loadIntegrations()

    toaster.add({
      title: 'Email account connected',
      description: `${integration.provider} account has been connected successfully`,
      color: 'success',
      icon: 'lucide:mail',
    })
  }
  catch (error) {
    console.error('Failed to save email integration:', error)
    toaster.add({
      title: 'Connection failed',
      description: 'Failed to connect email account. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
    })
  }
}

// Get email provider configuration
function getEmailProvider(providerName) {
  const emailProviders = {
    'Gmail': {
      id: 'gmail',
      name: 'Gmail',
      icon: 'logos:google-gmail',
      authType: 'oauth',
      description: 'Connect your Gmail account for email management and automation.',
      features: ['OAuth 2.0', 'Full sync', 'Real-time updates', 'Thread support'],
    },
    'Outlook': {
      id: 'outlook',
      name: 'Outlook',
      icon: 'simple-icons:microsoftoutlook',
      authType: 'oauth',
      description: 'Connect your Outlook/Hotmail account for email sync.',
      features: ['OAuth 2.0', 'Calendar sync', 'Contacts sync', 'Full mailbox access'],
    },
    'Yahoo Mail': {
      id: 'yahoo',
      name: 'Yahoo Mail',
      icon: 'simple-icons:yahoo',
      authType: 'app-password',
      description: 'Connect your Yahoo Mail account using app passwords.',
      features: ['App passwords', 'IMAP/SMTP', 'Secure connection', 'Full sync'],
    },
    'Exchange Server': {
      id: 'exchange',
      name: 'Exchange Server',
      icon: 'simple-icons:microsoftexchange',
      authType: 'password',
      description: 'Connect to Microsoft Exchange Server for enterprise email.',
      features: ['Enterprise support', 'Active Directory', 'Calendar sync', 'Global address book'],
    },
    'IMAP Server': {
      id: 'imap',
      name: 'IMAP Server',
      icon: 'lucide:mail',
      authType: 'password',
      description: 'Connect to any IMAP/SMTP server with custom settings.',
      features: ['Custom configuration', 'SSL/TLS support', 'Flexible setup', 'Any provider'],
    },
    'Custom Email': {
      id: 'custom',
      name: 'Custom Email',
      icon: 'lucide:server',
      authType: 'password',
      description: 'Configure a custom email server with advanced settings.',
      features: ['Advanced settings', 'Custom ports', 'Security options', 'Professional setup'],
    },
  }
  return emailProviders[providerName]
}

// Methods
function showModal(integrationName) {
  modalVisible.value[integrationName] = true
}

function closeModal(integrationName) {
  modalVisible.value[integrationName] = false
}

async function connectIntegration(integrationName) {
  if (apiKeys.value[integrationName]) {
    isSaving.value[integrationName] = true
    try {
      // Map integration names to provider IDs
      const providerMapping = {
        OpenAI: 'openai',
        Claude: 'anthropic',
        Grok: 'xai',
        Gemini: 'google',
        Mistral: 'mistral',
        Cohere: 'cohere',
        Perplexity: 'perplexity',
        Meta: 'meta',
        NVIDIA: 'nvidia',
        Groq: 'groq',
        Ollama: 'ollama',
        Stripe: 'stripe',
        Paypal: 'paypal',
      }

      const providerId = providerMapping[integrationName] || integrationName.toLowerCase()

      // Get provider config for LLM providers
      let defaultModel = ''
      if (['openai', 'anthropic', 'xai', 'google', 'mistral', 'cohere', 'perplexity', 'meta', 'nvidia', 'groq', 'ollama'].includes(providerId)) {
        const { getLLMProvider, getDefaultModel } = await import('../../config/llm-providers')
        const provider = getLLMProvider(providerId)
        const model = getDefaultModel(providerId)
        if (model) {
          defaultModel = model.id
        }
      }

      // Encrypt the API key before storing
      const encryptResponse = await $fetch('/api/integrations/encrypt', {
        method: 'POST',
        body: {
          apiKey: apiKeys.value[integrationName],
          provider: providerId,
        },
      })

      const integrationData = {
        provider: providerId,
        name: integrationName,
        description: `${integrationName} integration`,
        credentials: {
          apiKey: encryptResponse.encryptedKey,
          encryptedAt: new Date(encryptResponse.encryptedAt),
        },
        settings: {
          defaultModel,
          integrationType: integrationName === 'Claude'
            ? claudeIntegrationMethod.value
            : integrationName === 'Gemini'
              ? geminiIntegrationMethod.value
              : integrationName === 'Perplexity' ? perplexityMcpConfig.value : 'api',
        },
        isActive: true,
        isDefault: false,
        availableToProfiles: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await integrationsApi.create(integrationData)

      if (!connectedIntegrations.value.includes(integrationName)) {
        connectedIntegrations.value.push(integrationName)
      }

      // Reload integrations after successful creation
      await loadIntegrations()

      // Show success toast
      toaster.success('Integration connected', `Successfully connected to ${integrationName}`)

      console.log(`Connected to ${integrationName} successfully`)
    }
    catch (error) {
      console.error(`Failed to connect integration ${integrationName}:`, error)

      // Parse different types of errors for better user feedback
      let errorMessage = 'Please check your API key and try again.'

      if (error.response?.status === 401) {
        errorMessage = 'Invalid API key. Please check your credentials.'
      }
      else if (error.response?.status === 403) {
        errorMessage = 'API key does not have sufficient permissions.'
      }
      else if (error.response?.status === 429) {
        errorMessage = 'Rate limit exceeded. Please try again later.'
      }
      else if (error.response?.status === 500) {
        errorMessage = 'Server error. Please try again later.'
      }
      else if (error.message?.includes('Network Error')) {
        errorMessage = 'Network error. Please check your internet connection.'
      }
      else if (error.message?.includes('timeout')) {
        errorMessage = 'Request timeout. Please try again.'
      }
      else if (error.message) {
        errorMessage = error.message
      }

      // Show error toast with specific message
      toaster.error('Connection failed', `Failed to connect to ${integrationName}. ${errorMessage}`)
    }
    finally {
      isSaving.value[integrationName] = false
      // Clear the API key for security
      delete apiKeys.value[integrationName]
      // Use nextTick to ensure the modal state is properly updated
      await nextTick()
      modalVisible.value[integrationName] = false
    }
  }
}

async function authenticateWithClaude(method) {
  isAuthenticating.value = true
  try {
    // Initialize OAuth flow
    const response = await $fetch('/api/integrations/claude/oauth/init', {
      method: 'POST',
      body: {
        method, // 'google' or 'email'
        redirectUri: `${window.location.origin}/user/integrations`,
      },
    })

    if (response.authUrl) {
      // Open OAuth window
      const authWindow = window.open(
        response.authUrl,
        'Claude Authentication',
        'width=600,height=700',
      )

      // Listen for OAuth callback
      const checkInterval = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkInterval)
          // Check if authentication was successful
          checkAuthenticationStatus()
        }
      }, 1000)
    }
  }
  catch (error) {
    console.error('Failed to initiate Claude authentication:', error)
    toaster.error('Authentication failed', 'Could not start Claude authentication. Please try again.')
  }
  finally {
    isAuthenticating.value = false
  }
}

// Social OAuth authentication
async function authenticateWithSocial(provider) {
  isAuthenticating.value = true
  try {
    // Map display names to provider IDs
    const providerMapping = {
      Facebook: 'facebook',
      Instagram: 'instagram',
      LinkedIn: 'linkedin',
      Twitter: 'twitter',
      Reddit: 'reddit',
      Pinterest: 'pinterest',
      TikTok: 'tiktok',
      YouTube: 'youtube',
    }

    const providerId = providerMapping[provider] || provider.toLowerCase()

    // Initialize OAuth flow
    const response = await $fetch(`/api/integrations/${providerId}/oauth/init`, {
      method: 'POST',
      body: {
        redirectUri: `${window.location.origin}/api/integrations/${providerId}/oauth/callback`,
      },
    })

    if (response.authUrl) {
      // Open OAuth window
      const authWindow = window.open(
        response.authUrl,
        `${provider} Authentication`,
        'width=600,height=700',
      )

      // Listen for OAuth completion via postMessage
      const messageHandler = async (event) => {
        if (event.data?.type === 'oauth-success' && event.data?.provider === providerId) {
          window.removeEventListener('message', messageHandler)

          // Check authentication status
          await checkSocialAuthenticationStatus(provider, providerId)
        }
        else if (event.data?.type === 'oauth-error' && event.data?.provider === providerId) {
          window.removeEventListener('message', messageHandler)
          toaster.error('Authentication failed', event.data.error || `Failed to connect to ${provider}`)
        }
      }

      window.addEventListener('message', messageHandler)

      // Also check if window was closed manually
      const checkInterval = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkInterval)
          window.removeEventListener('message', messageHandler)
        }
      }, 1000)
    }
  }
  catch (error) {
    console.error(`Failed to initiate ${provider} authentication:`, error)
    toaster.error('Authentication failed', `Could not start ${provider} authentication. Please try again.`)
  }
  finally {
    isAuthenticating.value = false
  }
}

// Google Calendar connection handler
async function handleGoogleCalendarConnect() {
  try {
    await connectGoogleCalendar()
    toaster.show({
      title: 'Google Calendar Connected',
      message: 'Your Google Calendar has been successfully connected.',
      color: 'success',
      icon: 'lucide:calendar',
    })
    closeModal('Google Calendar')
  }
  catch (error) {
    toaster.error('Connection Failed', error.message || 'Failed to connect Google Calendar')
  }
}

// Google Calendar disconnect handler
async function handleGoogleCalendarDisconnect() {
  try {
    await disconnectGoogleCalendar()
    toaster.show({
      title: 'Google Calendar Disconnected',
      message: 'Your Google Calendar has been disconnected.',
      color: 'info',
      icon: 'lucide:calendar-off',
    })
  }
  catch (error) {
    toaster.error('Disconnect Failed', error.message || 'Failed to disconnect Google Calendar')
  }
}

// Google Calendar sync handlers
async function handleGoogleCalendarSync() {
  try {
    await syncFromGoogle()
  }
  catch (error) {
    toaster.error('Sync Failed', error.message || 'Failed to sync from Google Calendar')
  }
}

async function checkSocialAuthenticationStatus(provider, providerId) {
  try {
    const status = await $fetch(`/api/integrations/${providerId}/oauth/status`)

    if (status.authenticated) {
      // Create integration with OAuth data
      const integrationData = {
        provider: providerId,
        name: provider,
        description: `${provider} social media integration`,
        credentials: {
          type: 'oauth',
          accessToken: status.tokenData.accessToken,
          refreshToken: status.tokenData.refreshToken,
          expiresAt: status.tokenData.expiresAt ? new Date(status.tokenData.expiresAt) : null,
          encryptedAt: new Date(),
          accountId: status.profile.id,
          accountUsername: status.profile.username || status.profile.name,
        },
        settings: {
          integrationType: 'oauth',
          platformFeatures: status.features || [],
        },
        isActive: true,
        isDefault: false,
        availableToProfiles: true,
        category: 'social',
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await integrationsApi.create(integrationData)

      if (!connectedIntegrations.value.includes(provider)) {
        connectedIntegrations.value.push(provider)
      }

      await loadIntegrations()
      toaster.success('Integration connected', `Successfully connected to ${provider}`)

      // Close modal
      await nextTick()
      modalVisible.value[provider] = false
    }
    else {
      toaster.error('Authentication failed', `Could not complete ${provider} authentication.`)
    }
  }
  catch (error) {
    console.error(`Failed to check ${provider} authentication status:`, error)
  }
}

async function checkOllamaModels() {
  isLoadingOllamaModels.value = true
  try {
    const response = await $fetch(`${ollamaUrl.value}/api/tags`)
    ollamaModels.value = response.models || []
    return ollamaModels.value
  }
  catch (error) {
    console.error('Failed to fetch Ollama models:', error)
    ollamaModels.value = []
    toaster.error('Connection failed', 'Could not connect to Ollama. Make sure Ollama is running.')
    return []
  }
  finally {
    isLoadingOllamaModels.value = false
  }
}

async function connectOllama() {
  isSaving.value.Ollama = true
  try {
    const models = await checkOllamaModels()

    if (models.length === 0) {
      throw new Error('No models found in Ollama instance')
    }

    const integrationData = {
      provider: 'ollama',
      name: 'Ollama',
      description: 'Local Ollama instance integration',
      credentials: {
        baseUrl: ollamaUrl.value,
        type: 'local',
      },
      settings: {
        baseUrl: ollamaUrl.value,
        integrationType: 'local',
        availableModels: models.map(m => m.name),
        defaultModel: models[0]?.name || '',
      },
      isActive: true,
      isDefault: false,
      availableToProfiles: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    await integrationsApi.create(integrationData)

    if (!connectedIntegrations.value.includes('Ollama')) {
      connectedIntegrations.value.push('Ollama')
    }

    await loadIntegrations()
    toaster.success('Integration connected', `Successfully connected to Ollama with ${models.length} models`)

    await nextTick()
    modalVisible.value.Ollama = false
  }
  catch (error) {
    console.error('Failed to connect Ollama:', error)
    toaster.error('Connection failed', 'Failed to connect to Ollama. Please check the URL and ensure Ollama is running.')
  }
  finally {
    isSaving.value.Ollama = false
  }
}

function openClaudeCodeDocs() {
  window.open('https://docs.anthropic.com/en/docs/claude-code', '_blank')
}

function handleEmailIntegrationSuccess() {
  toaster.add({
    title: 'Success',
    description: `${currentEmailProvider.value} account connected successfully`,
    color: 'green',
  })
  // Refresh integrations list
  loadIntegrations()
}

async function validateClaudeApiKey(apiKey) {
  if (!apiKey || apiKey.length < 10) {
    return { valid: false, error: 'API key is too short' }
  }

  if (!apiKey.startsWith('sk-ant-api03-')) {
    return { valid: false, error: 'Invalid API key format. Claude API keys start with "sk-ant-api03-"' }
  }

  try {
    // Test the API key with a simple request
    const response = await $fetch('/api/integrations/claude/validate', {
      method: 'POST',
      body: { apiKey },
    })

    return { valid: true, ...response }
  }
  catch (error) {
    return {
      valid: false,
      error: error.message || 'Failed to validate API key. Please check your key and try again.',
    }
  }
}

async function testClaudeApiKey() {
  const apiKey = apiKeys.value.Claude
  if (!apiKey) {
    toaster.error('Validation failed', 'Please enter an API key first')
    return
  }

  isValidatingApiKey.value.Claude = true

  try {
    const result = await validateClaudeApiKey(apiKey)
    apiKeyValidation.value.Claude = result

    if (result.valid) {
      toaster.success('API key valid', 'Your Claude API key is working correctly')
    }
    else {
      toaster.error('API key invalid', result.error)
    }
  }
  catch (error) {
    console.error('Failed to validate Claude API key:', error)
    apiKeyValidation.value.Claude = {
      valid: false,
      error: 'Validation failed. Please try again.',
    }
    toaster.error('Validation failed', 'Could not validate API key. Please try again.')
  }
  finally {
    isValidatingApiKey.value.Claude = false
  }
}

async function checkAuthenticationStatus() {
  try {
    const status = await $fetch('/api/integrations/claude/oauth/status')
    if (status.authenticated) {
      // Create integration with OAuth credentials
      const integrationData = {
        provider: 'anthropic',
        name: 'Claude',
        description: 'Claude AI integration via subscription',
        credentials: {
          type: 'oauth',
          accessToken: status.accessToken,
          refreshToken: status.refreshToken,
          expiresAt: new Date(status.expiresAt),
        },
        settings: {
          defaultModel: 'claude-3-5-sonnet-latest',
          integrationType: 'subscription',
          authMethod: status.authMethod,
        },
        isActive: true,
        isDefault: false,
        availableToProfiles: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      await integrationsApi.create(integrationData)

      if (!connectedIntegrations.value.includes('Claude')) {
        connectedIntegrations.value.push('Claude')
      }

      await loadIntegrations()
      toaster.success('Integration connected', 'Successfully connected to Claude via subscription')

      // Close modal
      await nextTick()
      modalVisible.value.Claude = false
    }
    else {
      toaster.error('Authentication failed', 'Could not complete Claude authentication.')
    }
  }
  catch (error) {
    console.error('Failed to check authentication status:', error)
    toaster.error('Authentication check failed', 'Unable to verify authentication status. Please try again.')
  }
}

async function disconnectIntegration(integrationName, deleteType = 'soft') {
  try {
    // Find the integration in saved integrations
    const integration = savedIntegrations.value.find(int => int.name === integrationName)

    if (integration) {
      if (deleteType === 'hard') {
        // Hard delete - completely remove the integration
        await integrationsApi.remove(integration.id, { hardDelete: true })

        // Remove from saved integrations
        const savedIndex = savedIntegrations.value.findIndex(int => int.name === integrationName)
        if (savedIndex > -1) {
          savedIntegrations.value.splice(savedIndex, 1)
        }
      }
      else {
        // Soft delete by setting isActive to false
        await integrationsApi.update(integration.id, {
          isActive: false,
          updatedAt: new Date(),
        })
      }
    }

    // Remove from connected integrations
    const index = connectedIntegrations.value.indexOf(integrationName)
    if (index > -1) {
      connectedIntegrations.value.splice(index, 1)
    }

    // Clear the API key
    delete apiKeys.value[integrationName]

    // Reload integrations
    await loadIntegrations()

    // Show success toast
    const action = deleteType === 'hard' ? 'deleted' : 'deactivated'
    toaster.success(`Integration ${action}`, `Successfully ${action} ${integrationName}`)

    console.log(`${deleteType === 'hard' ? 'Deleted' : 'Disconnected'} ${integrationName}`)
  }
  catch (error) {
    console.error(`Failed to disconnect integration ${integrationName}:`, error)

    // Show error toast
    const action = deleteType === 'hard' ? 'delete' : 'disconnect from'
    toaster.error('Operation failed', `Failed to ${action} ${integrationName}. Please try again.`)
  }
}

// Get integration status (active/inactive)
function getIntegrationStatus(integrationName) {
  const integration = savedIntegrations.value.find(int => int.name === integrationName)
  return integration?.isActive ?? false
}

// Get integration details
function getIntegrationDetails(integrationName) {
  const integration = savedIntegrations.value.find(int => int.name === integrationName)
  if (!integration)
    return ''

  const details = []

  // Add model info for AI integrations
  if (integration.settings?.defaultModel) {
    details.push(`Model: ${integration.settings.defaultModel}`)
  }

  // Add last used info if available
  if (integration.lastUsedAt) {
    // Handle Firestore Timestamp
    const lastUsedTime = integration.lastUsedAt.toDate ? integration.lastUsedAt.toDate() : new Date(integration.lastUsedAt)
    const now = new Date()
    const diffMs = now.getTime() - lastUsedTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    let timeAgo = ''
    if (diffDays > 0) {
      timeAgo = `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    }
    else if (diffHours > 0) {
      timeAgo = `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    }
    else if (diffMins > 0) {
      timeAgo = `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
    }
    else {
      timeAgo = 'Just now'
    }

    details.push(`Last used: ${timeAgo}`)
  }

  return details.join(' • ')
}

// Toggle integration on/off
async function toggleIntegrationStatus(integrationName, newStatus) {
  try {
    isToggling.value[integrationName] = true
    const integration = savedIntegrations.value.find(int => int.name === integrationName)

    if (integration) {
      await integrationsApi.update(integration.id, {
        isActive: newStatus,
        updatedAt: new Date(),
      })

      // Update connected integrations list
      if (newStatus && !connectedIntegrations.value.includes(integrationName)) {
        connectedIntegrations.value.push(integrationName)
      }
      else if (!newStatus) {
        const index = connectedIntegrations.value.indexOf(integrationName)
        if (index > -1) {
          connectedIntegrations.value.splice(index, 1)
        }
      }

      await loadIntegrations()

      toaster.success(
        newStatus ? 'Integration enabled' : 'Integration disabled',
        `${integrationName} has been ${newStatus ? 'enabled' : 'disabled'}`,
      )
    }
  }
  catch (error) {
    console.error(`Failed to toggle integration ${integrationName}:`, error)
    toaster.error('Toggle failed', `Failed to ${newStatus ? 'enable' : 'disable'} ${integrationName}`)
  }
  finally {
    isToggling.value[integrationName] = false
  }
}

// Show confirmation dialog for disconnect
function confirmDisconnect(integrationName) {
  confirmDisconnectModal.value = {
    visible: true,
    integrationName,
    deleteType: 'soft', // Default to soft delete
  }
}

// Handle confirmed disconnect
async function handleConfirmedDisconnect() {
  const integrationName = confirmDisconnectModal.value.integrationName
  const deleteType = confirmDisconnectModal.value.deleteType
  confirmDisconnectModal.value.visible = false
  await disconnectIntegration(integrationName, deleteType)
}

// Show edit modal
function showEditModal(integrationName) {
  const integration = savedIntegrations.value.find(int => int.name === integrationName)
  if (integration) {
    // Initialize edit form with current values
    editSettings.value[integrationName] = {
      defaultModel: integration.settings?.defaultModel || '',
      maxTokens: integration.settings?.maxTokens || 1024,
      temperature: integration.settings?.temperature || 0.7,
      baseUrl: integration.settings?.baseUrl || '',
    }
    editModalVisible.value[integrationName] = true
  }
}

// Close edit modal
function closeEditModal(integrationName) {
  editModalVisible.value[integrationName] = false
  delete editApiKeys.value[integrationName]
  delete editSettings.value[integrationName]
}

// Update integration
async function updateIntegration(integrationName) {
  try {
    isUpdating.value[integrationName] = true
    const integration = savedIntegrations.value.find(int => int.name === integrationName)

    if (!integration) {
      throw new Error('Integration not found')
    }

    const updateData = {
      settings: {
        ...integration.settings,
        ...editSettings.value[integrationName],
      },
    }

    // If API key was changed, re-encrypt it
    if (editApiKeys.value[integrationName]) {
      const encryptResponse = await $fetch('/api/integrations/encrypt', {
        method: 'POST',
        body: {
          apiKey: editApiKeys.value[integrationName],
          provider: integration.provider,
        },
      })

      updateData.credentials = {
        ...integration.credentials,
        apiKey: encryptResponse.encryptedKey,
        encryptedAt: new Date(encryptResponse.encryptedAt),
      }
    }

    await integrationsApi.update(integration.id, updateData)

    // Reload integrations
    await loadIntegrations()

    toaster.success('Integration updated', `Successfully updated ${integrationName}`)
    closeEditModal(integrationName)
  }
  catch (error) {
    console.error(`Failed to update integration ${integrationName}:`, error)
    toaster.error('Update failed', `Failed to update ${integrationName}. Please try again.`)
  }
  finally {
    isUpdating.value[integrationName] = false
  }
}
</script>

<template>
  <div class="mt-8 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="border-primary-500 bg-primary-500/10 rounded-2xl border-2">
      <div class="p-4">
        <div class="gap-6 md:flex md:items-center md:justify-between">
          <BaseAvatar
            rounded="none"
            mask="blob"
            src="/img/avatars/15.svg"
            size="lg"
          />
          <div class="max-w-xs flex-1">
            <BaseParagraph weight="semibold" class="text-primary-700 dark:text-primary-400">
              Learn how to connect to our API
            </BaseParagraph>
            <BaseParagraph
              size="sm"
              class="text-primary-600 dark:text-primary-300"
            >
              We've put together a nice and simple tutorial.
            </BaseParagraph>
          </div>

          <div class="mt-6 flex items-center justify-start gap-3 md:ms-auto md:mt-0 md:justify-end md:space-x-reverse">
            <BaseButton rounded="md">
              Dismiss
            </BaseButton>
            <BaseButton
              variant="primary"
              rounded="md"
            >
              View Tutorial
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Connected integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          View and manage your connected integrations.
        </BaseParagraph>
      </div>

      <TairoInput
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <!-- Connected Integrations List -->
    <div v-if="connectedIntegrations.length > 0" class="mt-8 flow-root">
      <div class="divide-muted-200 dark:divide-muted-700 -my-5 divide-y">
        <div
          v-for="integrationName in connectedIntegrations"
          :key="integrationName"
          class="py-5"
        >
          <div class="sm:flex sm:items-center sm:justify-between sm:space-x-5">
            <div class="flex min-w-0 flex-1 items-center">
              <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mb-3 flex size-12 items-center justify-center rounded-xl border bg-white">
                <div class="bg-muted-100 dark:bg-muted-800 flex size-10 items-center justify-center rounded-lg">
                  <Icon
                    :name="getIntegrationStatus(integrationName) ? 'ph:check-circle' : 'ph:pause-circle'"
                    :class="getIntegrationStatus(integrationName) ? 'text-green-500' : 'text-muted-400'"
                    class="size-6"
                  />
                </div>
              </div>
              <div class="ms-4 min-w-0 flex-1 space-y-1">
                <BaseParagraph
                  size="sm"
                  weight="semibold"
                  class="text-muted-900 dark:text-muted-100 truncate"
                >
                  {{ integrationName }}
                </BaseParagraph>
                <BaseParagraph
                  size="xs"
                  weight="medium"
                  :class="getIntegrationStatus(integrationName) ? 'text-green-600 dark:text-green-400' : 'text-muted-500 dark:text-muted-400'"
                >
                  {{ getIntegrationStatus(integrationName) ? 'Connected and active' : 'Connected but disabled' }}
                </BaseParagraph>
                <BaseParagraph
                  v-if="getIntegrationDetails(integrationName)"
                  size="xs"
                  class="text-muted-400 dark:text-muted-500"
                >
                  {{ getIntegrationDetails(integrationName) }}
                </BaseParagraph>
              </div>
            </div>

            <div class="mt-4 flex items-center justify-between ps-14 sm:mt-0 sm:justify-end sm:space-x-6 sm:ps-0">
              <div class="flex items-center gap-2">
                <BaseButton size="sm" variant="soft" @click="showEditModal(integrationName)">
                  Edit
                </BaseButton>
                <BaseButton size="sm" variant="soft" color="danger" @click="confirmDisconnect(integrationName)">
                  Disconnect
                </BaseButton>
              </div>
              <BaseSwitchBall
                :model-value="getIntegrationStatus(integrationName)"
                :disabled="isToggling[integrationName]"
                size="xs"
                class="ms-auto"
                @update:model-value="toggleIntegrationStatus(integrationName, $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Connected Integrations Message -->
    <div v-else class="mt-8">
      <div class="text-center py-12">
        <Icon name="ph:plug" class="mx-auto h-12 w-12 text-muted-400" />
        <BaseHeading as="h3" size="md" weight="medium" class="mt-4 text-muted-900 dark:text-white">
          No connected integrations
        </BaseHeading>
        <BaseParagraph class="mt-2 text-muted-500 dark:text-muted-400">
          Connect to your favorite services below to get started.
        </BaseParagraph>
      </div>
    </div>

    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Available integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          View and manage your connected integrations.
        </BaseParagraph>
      </div>

      <TairoInput
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Accounting
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Available accounting integrations
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 space-y-8 divide-y pt-6"
      >
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar
            size="sm"
            src="/img/logos/companies/quickbooks-full.svg"
          />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Quickbooks
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              QuickBooks is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Quickbooks</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/xero-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Xero
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Xero is a user-friendly, simple accounting software that tracks
              your business income and expenses, and organises your financial
              information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Xero</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar
            size="sm"
            src="/img/logos/companies/freshbooks-full.svg"
          />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Freshbooks
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Freshbooks is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Freshbooks</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- AI/LLM Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          AI & Language Models
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Connect to powerful AI and language model providers
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 divide-y pt-6"
      >
        <!-- OpenAI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/openai-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              OpenAI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to OpenAI's powerful language models including GPT-4, GPT-3.5,
              and DALL-E for advanced AI capabilities in your applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('OpenAI')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to OpenAI</span>
            </BaseButton>
          </div>
        </div>

        <!-- Grok Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/grok-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Grok
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Grok AI by xAI for advanced language processing and
              real-time information access with conversational AI capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Grok')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Grok</span>
            </BaseButton>
          </div>
        </div>

        <!-- Claude Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/claude-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Claude
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Anthropic's Claude AI for safe, helpful, and honest AI
              assistance with advanced reasoning and analysis capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Claude')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Claude</span>
            </BaseButton>
          </div>
        </div>

        <!-- Gemini Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/google-gemini-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Gemini
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Google's Gemini AI for multimodal understanding, advanced
              reasoning, and code generation with state-of-the-art performance.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Gemini')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Gemini</span>
            </BaseButton>
          </div>
        </div>

        <!-- Mistral AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/mistral-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Mistral AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Mistral AI for advanced language models with excellent reasoning
              capabilities, supporting French and English with high performance.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Mistral')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Mistral</span>
            </BaseButton>
          </div>
        </div>

        <!-- Cohere AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/cohere-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Cohere AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Cohere AI for enterprise-grade NLP models optimized for
              business applications, semantic search, and text generation.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Cohere')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Cohere</span>
            </BaseButton>
          </div>
        </div>

        <!-- Perplexity AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/perplexity-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Perplexity AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Perplexity AI for real-time information access and advanced
              question answering with up-to-date search capabilities.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Perplexity')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Perplexity</span>
            </BaseButton>
          </div>
        </div>

        <!-- Meta AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/meta-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Meta AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Meta AI's Llama models for open-source language understanding
              and generation with strong multilingual support.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Meta')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Meta</span>
            </BaseButton>
          </div>
        </div>

        <!-- NVIDIA AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/nvidia-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              NVIDIA AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to NVIDIA AI for high-performance computing models optimized
              for GPU acceleration and enterprise-scale AI applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('NVIDIA')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to NVIDIA</span>
            </BaseButton>
          </div>
        </div>

        <!-- Groq AI Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/groq-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Groq AI
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Groq AI for ultra-fast inference speeds with their custom
              LPU technology, ideal for real-time AI applications.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Groq')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Groq</span>
            </BaseButton>
          </div>
        </div>

        <!-- Ollama Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/ollama-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Ollama
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to your local Ollama instance to run open-source language models
              privately on your own hardware with complete data control.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Ollama')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Ollama</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Other
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Other available integrations
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 divide-y pt-6"
      >
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/zapier-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Zapier
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Zapier is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Zapier</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/google-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Google Suite
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Google is a user-friendly, simple accounting software that
              tracks your business income and expenses, and organises your
              financial information for you, eliminating manual data entry.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Google</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/stripe-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Stripe
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Stripe payment processing integration for secure online transactions.
              Accept credit cards and digital payments with Stripe's powerful platform.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Stripe')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Stripe</span>
            </BaseButton>
          </div>
        </div>
        <!-- Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/paypal-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Paypal
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Paypal payment processing integration for secure online transactions.
              Accept payments from customers worldwide with PayPal's trusted platform.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Paypal')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Paypal</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Social Media Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Social Media
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Connect your social media accounts for seamless content management
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div
        class="divide-muted-200 dark:divide-muted-800 divide-y pt-6"
      >
        <!-- Facebook Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/facebook-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Facebook
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Facebook account to manage pages, posts, and insights.
              Schedule content and track engagement across your Facebook presence.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Facebook')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Facebook</span>
            </BaseButton>
          </div>
        </div>

        <!-- Instagram Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/instagram-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Instagram
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Instagram Business account to post photos, stories, and reels.
              Requires a business or creator account for full functionality.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Instagram')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Instagram</span>
            </BaseButton>
          </div>
        </div>

        <!-- LinkedIn Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/linkedin-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              LinkedIn
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your LinkedIn profile or company page to share professional content,
              articles, and updates with your network.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('LinkedIn')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to LinkedIn</span>
            </BaseButton>
          </div>
        </div>

        <!-- X (Twitter) Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/twitter-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              X (Twitter)
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your X account to post tweets, create threads, and engage with
              your audience in real-time conversations.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Twitter')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to X</span>
            </BaseButton>
          </div>
        </div>

        <!-- Reddit Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/reddit-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Reddit
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Reddit account to participate in communities, share content,
              and manage your posts across different subreddits.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Reddit')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Reddit</span>
            </BaseButton>
          </div>
        </div>

        <!-- Pinterest Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/pinterest-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Pinterest
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Pinterest account to create pins, manage boards, and track
              analytics for your visual content strategy.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('Pinterest')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Pinterest</span>
            </BaseButton>
          </div>
        </div>

        <!-- TikTok Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/tiktok-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              TikTok
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your TikTok account to upload videos, track performance, and
              engage with your audience on the fastest-growing social platform.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('TikTok')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to TikTok</span>
            </BaseButton>
          </div>
        </div>

        <!-- YouTube Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm" src="/img/logos/companies/youtube-full.svg" />
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              YouTube
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your YouTube channel to upload videos, manage playlists, and
              track analytics for your video content.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showModal('YouTube')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to YouTube</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Email Accounts
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Connect your email accounts for unified inbox management
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div class="divide-muted-200 dark:divide-muted-800 divide-y pt-6">
        <!-- Gmail Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="logos:google-gmail" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Gmail
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Gmail account for email management and automation using secure OAuth2 authentication.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('Gmail')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Gmail</span>
            </BaseButton>
          </div>
        </div>

        <!-- Outlook Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="simple-icons:microsoftoutlook" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Outlook
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Outlook/Hotmail account for email sync using Microsoft OAuth.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('Outlook')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Outlook</span>
            </BaseButton>
          </div>
        </div>

        <!-- Yahoo Mail Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="logos:yahoo" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Yahoo Mail
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Yahoo Mail account using app passwords for secure access.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('Yahoo Mail')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect to Yahoo</span>
            </BaseButton>
          </div>
        </div>

        <!-- IMAP Server Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="lucide:mail" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              IMAP Server
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to any IMAP/SMTP server with custom settings for full email integration.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('IMAP Server')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect IMAP</span>
            </BaseButton>
          </div>
        </div>

        <!-- Exchange Server Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="simple-icons:microsoftexchange" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Exchange Server
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect to Microsoft Exchange Server for enterprise email with advanced features.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('Exchange Server')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect Exchange</span>
            </BaseButton>
          </div>
        </div>

        <!-- Custom Email Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="lucide:server" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Custom Email Server
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Configure a custom email server with advanced settings and professional setup options.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              @click="showEmailModal('Custom Email')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect Custom</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Group -->
    <div>
      <!-- Header -->
      <div class="border-muted-200 dark:border-muted-800 border-b py-6">
        <BaseHeading
          as="h2"
          size="lg"
          weight="medium"
          class="text-muted-800 dark:text-white"
        >
          Calendar Integrations
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400 max-w-sm">
          Sync your calendar events across all platforms
        </BaseParagraph>
      </div>
      <!-- Content -->
      <div class="divide-muted-200 dark:divide-muted-800 divide-y pt-6">
        <!-- Google Calendar Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="logos:google-calendar" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Google Calendar
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Sync your Google Calendar events with your workspace calendar for seamless scheduling and unified event management.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              v-if="!isGoogleCalendarConnected"
              rounded="md"
              size="sm"
              :loading="isGoogleCalendarConnecting"
              @click="showModal('Google Calendar')"
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Connect Google Calendar</span>
            </BaseButton>
            <div v-else class="flex items-center gap-2">
              <div class="flex items-center gap-2 text-green-600 dark:text-green-400">
                <Icon name="lucide:check-circle" class="size-4" />
                <span class="text-sm font-medium">Connected</span>
              </div>
              <BaseButton
                variant="outline"
                size="sm"
                @click="showModal('Google Calendar')"
              >
                <Icon name="lucide:settings" class="size-4" />
                <span>Manage</span>
              </BaseButton>
            </div>
          </div>
        </div>

        <!-- Outlook Calendar Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="simple-icons:microsoftoutlook" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Outlook Calendar
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Connect your Outlook calendar for seamless event management and scheduling across Microsoft 365.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              disabled
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Coming Soon</span>
            </BaseButton>
          </div>
        </div>

        <!-- Apple Calendar Item -->
        <div class="flex flex-col gap-3 py-6 lg:flex-row">
          <BaseAvatar size="sm">
            <Icon name="logos:apple" class="h-8 w-8" />
          </BaseAvatar>
          <div class="max-w-lg">
            <BaseHeading
              as="h4"
              size="lg"
              weight="medium"
              lead="tight"
              class="text-muted-800 mb-1 dark:text-white"
            >
              Apple Calendar (iCloud)
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 max-w-sm"
            >
              Sync events with your Apple Calendar through iCloud for seamless integration across all your Apple devices.
            </BaseParagraph>
          </div>
          <div class="lg:ms-auto">
            <BaseButton
              rounded="md"
              size="sm"
              disabled
            >
              <Icon name="solar:link-circle-linear" class="size-4" />
              <span>Coming Soon</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Email Integration Modal -->
  <TairoEmailIntegrationModal
    v-if="currentEmailProvider"
    :visible="emailModalVisible"
    :provider-name="currentEmailProvider"
    @success="handleEmailIntegrationSave"
    @close="closeEmailModal"
  />

  <!-- Global Modal Components -->
  <TairoModal :open="modalVisible.OpenAI" size="sm" @close="closeModal('OpenAI')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to OpenAI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.OpenAI" placeholder="Enter OpenAI API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your OpenAI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.OpenAI"
          :disabled="!apiKeys.OpenAI || isSaving.OpenAI"
          @click="connectIntegration('OpenAI')"
        >
          <span v-if="!isSaving.OpenAI">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('OpenAI')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Grok" size="sm" @close="closeModal('Grok')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Grok
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Grok" placeholder="Enter Grok API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your xAI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Grok"
          :disabled="!apiKeys.Grok || isSaving.Grok"
          @click="connectIntegration('Grok')"
        >
          <span v-if="!isSaving.Grok">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Grok')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Claude" size="md" @close="closeModal('Claude')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Claude
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <!-- Integration Method Tabs -->
      <div class="flex gap-2 mb-4">
        <BaseButton
          :variant="claudeIntegrationMethod === 'api' ? 'primary' : 'ghost'"
          size="sm"
          @click="claudeIntegrationMethod = 'api'"
        >
          API Key
        </BaseButton>
        <BaseButton
          :variant="claudeIntegrationMethod === 'claude-code' ? 'primary' : 'ghost'"
          size="sm"
          @click="claudeIntegrationMethod = 'claude-code'"
        >
          Claude Code
        </BaseButton>
      </div>

      <!-- API Key Method -->
      <div v-if="claudeIntegrationMethod === 'api'" class="space-y-4">
        <div class="space-y-2">
          <BaseInput
            v-model="apiKeys.Claude"
            placeholder="sk-ant-api03-..."
            type="password"
            label="Claude API Key"
            :class="{
              'border-red-300 dark:border-red-700': apiKeyValidation.Claude && !apiKeyValidation.Claude.valid,
              'border-green-300 dark:border-green-700': apiKeyValidation.Claude && apiKeyValidation.Claude.valid,
            }"
          />

          <div class="flex gap-2 items-center">
            <BaseButton
              variant="soft"
              size="sm"
              :loading="isValidatingApiKey.Claude"
              :disabled="!apiKeys.Claude || isValidatingApiKey.Claude"
              @click="testClaudeApiKey"
            >
              <Icon name="lucide:check-circle" class="w-4 h-4" />
              <span>Test API Key</span>
            </BaseButton>

            <div v-if="apiKeyValidation.Claude" class="flex items-center gap-1">
              <Icon
                :name="apiKeyValidation.Claude.valid ? 'lucide:check-circle' : 'lucide:x-circle'"
                :class="apiKeyValidation.Claude.valid ? 'text-green-600' : 'text-red-600'"
                class="w-4 h-4"
              />
              <span
                :class="apiKeyValidation.Claude.valid ? 'text-green-600' : 'text-red-600'"
                class="text-xs"
              >
                {{ apiKeyValidation.Claude.valid ? 'Valid API key' : apiKeyValidation.Claude.error }}
              </span>
            </div>
          </div>
        </div>

        <BaseParagraph size="xs" class="text-muted-500">
          Your API key is securely stored and encrypted. You can find your API key in your <a href="https://console.anthropic.com/settings/keys" target="_blank" class="text-primary-500 hover:underline">Anthropic dashboard</a>.
        </BaseParagraph>

        <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
          <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm text-blue-800 dark:text-blue-200">API key integration allows direct access to Claude's API from your server-side code.</span>
        </div>
      </div>

      <!-- Claude Code Method -->
      <div v-if="claudeIntegrationMethod === 'claude-code'" class="space-y-4">
        <div class="flex items-start gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg">
          <Icon name="lucide:terminal" class="w-5 h-5 text-purple-600 dark:text-purple-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm font-semibold text-purple-800 dark:text-purple-200">Claude Code Integration</span>
        </div>

        <div class="space-y-3">
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
            Use your existing Claude Code installation for seamless AI assistance directly in your development workflow.
          </BaseParagraph>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              What is Claude Code?
            </BaseParagraph>
            <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
              <li>Official Anthropic CLI tool for developers</li>
              <li>Works with both API keys and Claude Pro subscriptions</li>
              <li>Integrated with your terminal and development workflow</li>
              <li>Supports advanced features like tool use and file context</li>
            </ul>
          </div>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              Installation:
            </BaseParagraph>
            <pre class="mt-2 p-3 bg-muted-200 dark:bg-muted-800 rounded text-sm overflow-x-auto"><code># Install Claude Code CLI
curl -sSL https://claude.ai/install.sh | bash

# Or download from GitHub
# https://github.com/anthropics/claude-code</code></pre>
          </div>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              Integration Options:
            </BaseParagraph>
            <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
              <li><strong>API Key:</strong> Use your Anthropic API key with Claude Code</li>
              <li><strong>Subscription:</strong> Authenticate with your Claude Pro account</li>
              <li><strong>MCP Servers:</strong> Enhanced capabilities with Model Context Protocol</li>
            </ul>
          </div>

          <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
            <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
            <div class="text-sm text-blue-800 dark:text-blue-200">
              <p class="font-medium mb-1">
                Pro Tip:
              </p>
              <p>Claude Code can automatically detect and use your authentication, whether through API keys or your Claude Pro subscription.</p>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <BaseParagraph size="sm" weight="medium">
            Choose your authentication method:
          </BaseParagraph>
          <div class="flex gap-3">
            <BaseButton
              variant="soft"
              @click="claudeIntegrationMethod = 'api'"
            >
              <Icon name="lucide:key" class="size-4" />
              <span>Use API Key</span>
            </BaseButton>
            <BaseButton
              variant="soft"
              @click="openClaudeCodeDocs"
            >
              <Icon name="lucide:external-link" class="size-4" />
              <span>Claude Code Documentation</span>
            </BaseButton>
          </div>
        </div>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Claude"
          :disabled="claudeIntegrationMethod === 'claude-code' || (!apiKeys.Claude || isSaving.Claude || (claudeIntegrationMethod === 'api' && apiKeyValidation.Claude && !apiKeyValidation.Claude.valid))"
          @click="connectIntegration('Claude')"
        >
          <span v-if="!isSaving.Claude">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Claude')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Stripe" size="sm" @close="closeModal('Stripe')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Stripe
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Stripe" placeholder="Enter Stripe API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Stripe dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Stripe"
          :disabled="!apiKeys.Stripe || isSaving.Stripe"
          @click="connectIntegration('Stripe')"
        >
          <span v-if="!isSaving.Stripe">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Stripe')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Paypal" size="sm" @close="closeModal('Paypal')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Paypal
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Paypal" placeholder="Enter Paypal API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your PayPal dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Paypal"
          :disabled="!apiKeys.Paypal || isSaving.Paypal"
          @click="connectIntegration('Paypal')"
        >
          <span v-if="!isSaving.Paypal">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Paypal')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Mistral" size="sm" @close="closeModal('Mistral')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Mistral AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Mistral" placeholder="Enter Mistral API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Mistral AI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Mistral"
          :disabled="!apiKeys.Mistral || isSaving.Mistral"
          @click="connectIntegration('Mistral')"
        >
          <span v-if="!isSaving.Mistral">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Mistral')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Cohere" size="sm" @close="closeModal('Cohere')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Cohere AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Cohere" placeholder="Enter Cohere API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Cohere dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Cohere"
          :disabled="!apiKeys.Cohere || isSaving.Cohere"
          @click="connectIntegration('Cohere')"
        >
          <span v-if="!isSaving.Cohere">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Cohere')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Perplexity" size="md" @close="closeModal('Perplexity')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Perplexity AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <!-- Integration Method Tabs -->
      <div class="flex gap-2 mb-4">
        <BaseButton
          :variant="perplexityMcpConfig === 'api' ? 'primary' : 'ghost'"
          size="sm"
          @click="perplexityMcpConfig = 'api'"
        >
          API Only
        </BaseButton>
        <BaseButton
          :variant="perplexityMcpConfig === 'mcp' ? 'primary' : 'ghost'"
          size="sm"
          @click="perplexityMcpConfig = 'mcp'"
        >
          MCP Server
        </BaseButton>
      </div>

      <!-- API Method -->
      <div v-if="perplexityMcpConfig === 'api'" class="space-y-4">
        <BaseInput v-model="apiKeys.Perplexity" placeholder="Enter Perplexity API Key" type="password" label="API Key" />
        <BaseParagraph size="xs" class="text-muted-500">
          Your API key is securely stored and encrypted. You can find your API key in your <a href="https://www.perplexity.ai/settings/api" target="_blank" class="text-primary-500 hover:underline">Perplexity dashboard</a>.
        </BaseParagraph>
        <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
          <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm text-blue-800 dark:text-blue-200">API integration provides direct access to Perplexity's Sonar models with real-time web search capabilities.</span>
        </div>
      </div>

      <!-- MCP Method -->
      <div v-if="perplexityMcpConfig === 'mcp'" class="space-y-4">
        <div class="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
          <Icon name="lucide:zap" class="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm font-semibold text-green-800 dark:text-green-200">MCP Server Integration</span>
        </div>

        <div class="space-y-3">
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
            Set up Perplexity as a Model Context Protocol server for enhanced Claude Desktop integration with real-time web search.
          </BaseParagraph>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              MCP Server Configuration:
            </BaseParagraph>
            <div class="space-y-2 text-sm text-muted-600 dark:text-muted-400">
              <p><strong>Server Name:</strong> perplexity-mcp</p>
              <p><strong>Install Command:</strong> <code class="bg-muted-200 dark:bg-muted-800 px-1 rounded">uvx perplexity-mcp</code></p>
              <p><strong>Environment Variables:</strong> PERPLEXITY_API_KEY, PERPLEXITY_MODEL</p>
            </div>
          </div>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              Claude Desktop Config:
            </BaseParagraph>
            <pre class="text-xs bg-muted-200 dark:bg-muted-800 p-2 rounded overflow-x-auto"><code>{
  "mcpServers": {
    "perplexity-mcp": {
      "env": {
        "PERPLEXITY_API_KEY": "YOUR_API_KEY_HERE",
        "PERPLEXITY_MODEL": "sonar"
      },
      "command": "uvx",
      "args": ["perplexity-mcp"]
    }
  }
}</code></pre>
          </div>
        </div>

        <BaseInput v-model="apiKeys.Perplexity" placeholder="Enter Perplexity API Key" type="password" label="API Key" />
        <BaseParagraph size="xs" class="text-muted-500">
          Enter your API key to save it for MCP server configuration. Get your key from <a href="https://www.perplexity.ai/settings/api" target="_blank" class="text-primary-500 hover:underline">Perplexity dashboard</a>.
        </BaseParagraph>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Perplexity"
          :disabled="!apiKeys.Perplexity || isSaving.Perplexity"
          @click="connectIntegration('Perplexity')"
        >
          <span v-if="!isSaving.Perplexity">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Perplexity')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Meta" size="sm" @close="closeModal('Meta')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Meta AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Meta" placeholder="Enter Meta API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Meta AI dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Meta"
          :disabled="!apiKeys.Meta || isSaving.Meta"
          @click="connectIntegration('Meta')"
        >
          <span v-if="!isSaving.Meta">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Meta')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.NVIDIA" size="sm" @close="closeModal('NVIDIA')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to NVIDIA AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.NVIDIA" placeholder="Enter NVIDIA API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your NVIDIA dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.NVIDIA"
          :disabled="!apiKeys.NVIDIA || isSaving.NVIDIA"
          @click="connectIntegration('NVIDIA')"
        >
          <span v-if="!isSaving.NVIDIA">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('NVIDIA')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Groq" size="sm" @close="closeModal('Groq')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Groq AI
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseInput v-model="apiKeys.Groq" placeholder="Enter Groq API Key" type="password" label="API Key" />
      <BaseParagraph size="xs" class="text-muted-500">
        Your API key is securely stored and encrypted. You can find your API key in your Groq dashboard.
      </BaseParagraph>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Groq"
          :disabled="!apiKeys.Groq || isSaving.Groq"
          @click="connectIntegration('Groq')"
        >
          <span v-if="!isSaving.Groq">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Groq')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Ollama" size="md" @close="closeModal('Ollama')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Ollama
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm text-blue-800 dark:text-blue-200">Ollama runs models locally on your machine for complete privacy and control.</span>
      </div>

      <BaseInput
        v-model="ollamaUrl"
        placeholder="http://localhost:11434"
        label="Ollama URL"
        help="URL where your Ollama instance is running"
      />

      <div class="flex gap-2">
        <BaseButton
          variant="soft"
          size="sm"
          :loading="isLoadingOllamaModels"
          @click="checkOllamaModels"
        >
          <Icon name="lucide:refresh-cw" class="w-4 h-4" />
          <span>Test Connection & Load Models</span>
        </BaseButton>
      </div>

      <div v-if="ollamaModels.length > 0" class="space-y-2">
        <BaseParagraph size="sm" weight="medium">
          Available Models ({{ ollamaModels.length }})
        </BaseParagraph>
        <div class="max-h-32 overflow-y-auto space-y-1 p-3 bg-muted-50 dark:bg-muted-900 rounded-lg">
          <div v-for="model in ollamaModels" :key="model.name" class="flex items-center justify-between text-xs">
            <span class="font-medium">{{ model.name }}</span>
            <span class="text-muted-500">{{ (model.size / 1e9).toFixed(1) }} GB</span>
          </div>
        </div>
      </div>

      <div v-else-if="!isLoadingOllamaModels && ollamaModels.length === 0" class="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
        <div class="flex items-start gap-3">
          <Icon name="lucide:alert-triangle" class="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
          <div class="text-sm text-amber-800 dark:text-amber-200">
            <p class="font-medium mb-1">
              No models found
            </p>
            <p>Make sure Ollama is running and you have models installed. Run <code class="bg-amber-100 dark:bg-amber-800 px-1 rounded">ollama list</code> to see available models.</p>
          </div>
        </div>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Ollama"
          :disabled="ollamaModels.length === 0 || isSaving.Ollama"
          @click="connectOllama"
        >
          <span v-if="!isSaving.Ollama">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Ollama')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <TairoModal :open="modalVisible.Gemini" size="md" @close="closeModal('Gemini')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Gemini
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <!-- Integration Method Tabs -->
      <div class="flex gap-2 mb-4">
        <BaseButton
          :variant="geminiIntegrationMethod === 'api' ? 'primary' : 'ghost'"
          size="sm"
          @click="geminiIntegrationMethod = 'api'"
        >
          API Key
        </BaseButton>
        <BaseButton
          :variant="geminiIntegrationMethod === 'sdk' ? 'primary' : 'ghost'"
          size="sm"
          @click="geminiIntegrationMethod = 'sdk'"
        >
          SDK
        </BaseButton>
      </div>

      <!-- API Key Method -->
      <div v-if="geminiIntegrationMethod === 'api'" class="space-y-4">
        <BaseInput v-model="apiKeys.Gemini" placeholder="Enter Gemini API Key" type="password" label="API Key" />
        <BaseParagraph size="xs" class="text-muted-500">
          Your API key is securely stored and encrypted. You can find your API key in <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-primary-500 hover:underline">Google AI Studio</a>.
        </BaseParagraph>
        <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
          <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm">API key integration allows direct access to Gemini's API from your server-side code.</span>
        </div>
      </div>

      <!-- SDK Method -->
      <div v-if="geminiIntegrationMethod === 'sdk'" class="space-y-4">
        <div class="flex items-start gap-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
          <Icon name="lucide:alert-triangle" class="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
          <span class="text-sm font-semibold">SDK Setup Instructions</span>
        </div>

        <div class="space-y-3">
          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              1. Install the SDK
            </BaseParagraph>
            <pre class="mt-2 p-3 bg-muted-200 dark:bg-muted-800 rounded text-sm overflow-x-auto"><code>npm install @google/generative-ai</code></pre>
          </div>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              2. Initialize in your code
            </BaseParagraph>
            <pre class="mt-2 p-3 bg-muted-200 dark:bg-muted-800 rounded text-sm overflow-x-auto"><code>import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);</code></pre>
          </div>

          <div class="p-4 bg-muted-100 dark:bg-muted-900 rounded-lg">
            <BaseParagraph size="sm" weight="semibold" class="mb-2">
              3. Create server API endpoint
            </BaseParagraph>
            <pre class="mt-2 p-3 bg-muted-200 dark:bg-muted-800 rounded text-sm overflow-x-auto"><code>// server/api/gemini/chat.post.ts
export default defineEventHandler(async (event) => {
  const { messages } = await readBody(event);

  const model = genAI.getGenerativeModel({
    model: 'gemini-1.5-pro'
  });

  const result = await model.generateContent(messages);
  const response = await result.response;

  return { text: response.text() };
});</code></pre>
          </div>
        </div>

        <BaseInput v-model="apiKeys.Gemini" placeholder="Enter Gemini API Key for server configuration" type="password" label="API Key" />
        <BaseParagraph size="xs" class="text-muted-500">
          Enter your API key to save it for server-side configuration. Get your key from <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-primary-500 hover:underline">Google AI Studio</a>.
        </BaseParagraph>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isSaving.Gemini"
          :disabled="!apiKeys.Gemini || isSaving.Gemini"
          @click="connectIntegration('Gemini')"
        >
          <span v-if="!isSaving.Gemini">Connect</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Gemini')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Edit Modal for Connected Integrations -->
  <TairoModal
    v-for="integrationName in connectedIntegrations"
    :key="`edit-${integrationName}`"
    :open="editModalVisible[integrationName]"
    size="md"
    @close="closeEditModal(integrationName)"
  >
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Edit {{ integrationName }} Integration
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <!-- API Key Update (Optional) -->
      <div>
        <BaseInput
          v-model="editApiKeys[integrationName]"
          placeholder="Enter new API key (leave blank to keep current)"
          type="password"
          label="API Key"
        />
        <BaseParagraph size="xs" class="text-muted-500 mt-1">
          Only enter a new API key if you want to change it. Leave blank to keep the current key.
        </BaseParagraph>
      </div>

      <!-- Model Selection for AI Integrations -->
      <div v-if="['OpenAI', 'Claude', 'Grok', 'Gemini', 'Mistral', 'Cohere', 'Perplexity', 'Meta', 'NVIDIA', 'Groq'].includes(integrationName)">
        <BaseInput
          v-model="editSettings[integrationName].defaultModel"
          label="Default Model"
          placeholder="e.g., gpt-4, claude-3-5-sonnet-latest"
        />
      </div>

      <!-- Max Tokens -->
      <div v-if="['OpenAI', 'Claude', 'Grok', 'Gemini', 'Mistral', 'Cohere', 'Perplexity', 'Meta', 'NVIDIA', 'Groq'].includes(integrationName)">
        <BaseInput
          v-model.number="editSettings[integrationName].maxTokens"
          type="number"
          label="Max Tokens"
          placeholder="1024"
          min="1"
          max="100000"
        />
      </div>

      <!-- Temperature -->
      <div v-if="['OpenAI', 'Claude', 'Grok', 'Gemini', 'Mistral', 'Cohere', 'Perplexity', 'Meta', 'NVIDIA', 'Groq'].includes(integrationName)">
        <BaseInput
          v-model.number="editSettings[integrationName].temperature"
          type="number"
          label="Temperature"
          placeholder="0.7"
          min="0"
          max="2"
          step="0.1"
        />
      </div>

      <!-- Custom Base URL -->
      <div>
        <BaseInput
          v-model="editSettings[integrationName].baseUrl"
          label="Custom Base URL (Optional)"
          placeholder="Leave blank for default endpoint"
        />
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isUpdating[integrationName]"
          :disabled="isUpdating[integrationName]"
          @click="updateIntegration(integrationName)"
        >
          <span v-if="!isUpdating[integrationName]">Update</span>
          <span v-else>Updating...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeEditModal(integrationName)">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Disconnect Confirmation Modal -->
  <TairoModal
    :open="confirmDisconnectModal.visible"
    size="md"
    @close="confirmDisconnectModal.visible = false"
  >
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Disconnect {{ confirmDisconnectModal.integrationName }}
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <BaseParagraph>
        How would you like to disconnect from {{ confirmDisconnectModal.integrationName }}?
      </BaseParagraph>

      <!-- Disconnect Options -->
      <div class="space-y-3">
        <!-- Deactivate Option -->
        <div
          class="border rounded-lg p-4 cursor-pointer transition-colors"
          :class="confirmDisconnectModal.deleteType === 'soft'
            ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
            : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600'"
          @click="confirmDisconnectModal.deleteType = 'soft'"
        >
          <div class="flex items-start gap-3">
            <BaseRadio
              :model-value="confirmDisconnectModal.deleteType === 'soft'"
              name="deleteType"
              value="soft"
            />
            <div class="flex-1">
              <BaseHeading as="h4" size="sm" weight="medium" class="mb-1">
                Deactivate Integration
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-500">
                Temporarily disable this integration. Your API key and settings will be preserved,
                and you can re-enable it anytime using the toggle switch.
              </BaseParagraph>
            </div>
          </div>
        </div>

        <!-- Delete Option -->
        <div
          class="border rounded-lg p-4 cursor-pointer transition-colors"
          :class="confirmDisconnectModal.deleteType === 'hard'
            ? 'border-danger-500 bg-danger-50 dark:bg-danger-900/20'
            : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600'"
          @click="confirmDisconnectModal.deleteType = 'hard'"
        >
          <div class="flex items-start gap-3">
            <BaseRadio
              :model-value="confirmDisconnectModal.deleteType === 'hard'"
              name="deleteType"
              value="hard"
            />
            <div class="flex-1">
              <BaseHeading as="h4" size="sm" weight="medium" class="mb-1">
                Delete Integration
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-500">
                Permanently remove this integration. Your API key and all settings will be deleted.
                You'll need to reconfigure everything if you want to connect again.
              </BaseParagraph>
            </div>
          </div>
        </div>
      </div>

      <!-- Warning for hard delete -->
      <div
        v-if="confirmDisconnectModal.deleteType === 'hard'"
        class="flex items-start gap-3 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg"
      >
        <Icon name="lucide:alert-triangle" class="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm text-red-800 dark:text-red-200">
          <strong>Warning:</strong> This action cannot be undone. All configuration data for this integration will be permanently deleted.
        </span>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          :color="confirmDisconnectModal.deleteType === 'hard' ? 'danger' : 'warning'"
          @click="handleConfirmedDisconnect"
        >
          {{ confirmDisconnectModal.deleteType === 'hard' ? 'Delete' : 'Deactivate' }}
        </BaseButton>
        <BaseButton
          variant="ghost"
          @click="confirmDisconnectModal.visible = false"
        >
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Social Media OAuth Modals -->
  <!-- Facebook Modal -->
  <TairoModal :open="modalVisible.Facebook" size="md" @close="closeModal('Facebook')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Facebook
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">You'll be redirected to Facebook to authorize access to your account.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your Facebook account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Manage pages and posts</li>
          <li>Track page insights and engagement</li>
          <li>Schedule content publication</li>
          <li>Monitor comments and messages</li>
        </ul>
      </div>

      <div class="flex items-start gap-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
        <Icon name="lucide:shield-alert" class="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">We only request necessary permissions to manage your pages. Your personal data remains secure.</span>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('Facebook')"
        >
          <Icon name="logos:facebook" class="size-4" />
          <span v-if="!isAuthenticating">Connect with Facebook</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Facebook')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Instagram Modal -->
  <TairoModal :open="modalVisible.Instagram" size="md" @close="closeModal('Instagram')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Instagram
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm text-blue-800 dark:text-blue-200">Requires an Instagram Business or Creator account for full functionality.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your Instagram account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Post photos, videos, and carousels</li>
          <li>Share stories and reels</li>
          <li>View insights and analytics</li>
          <li>Manage comments and direct messages</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('Instagram')"
        >
          <Icon name="logos:instagram-icon" class="size-4" />
          <span v-if="!isAuthenticating">Connect with Instagram</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Instagram')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- LinkedIn Modal -->
  <TairoModal :open="modalVisible.LinkedIn" size="md" @close="closeModal('LinkedIn')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to LinkedIn
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your LinkedIn profile or company page for professional networking.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your LinkedIn account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Share professional updates and articles</li>
          <li>Manage company page posts</li>
          <li>Track post engagement</li>
          <li>Build your professional network</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('LinkedIn')"
        >
          <Icon name="logos:linkedin-icon" class="size-4" />
          <span v-if="!isAuthenticating">Connect with LinkedIn</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('LinkedIn')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Twitter/X Modal -->
  <TairoModal :open="modalVisible.Twitter" size="md" @close="closeModal('Twitter')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to X (Twitter)
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your X account to share updates and engage with your audience.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your X account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Post tweets and threads</li>
          <li>Share images and videos</li>
          <li>Create polls</li>
          <li>Schedule posts</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('Twitter')"
        >
          <Icon name="logos:twitter" class="size-4" />
          <span v-if="!isAuthenticating">Connect with X</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Twitter')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Reddit Modal -->
  <TairoModal :open="modalVisible.Reddit" size="md" @close="closeModal('Reddit')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Reddit
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your Reddit account to participate in communities.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your Reddit account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Post to subreddits</li>
          <li>Share links and images</li>
          <li>Participate in discussions</li>
          <li>Manage your posts and comments</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('Reddit')"
        >
          <Icon name="logos:reddit-icon" class="size-4" />
          <span v-if="!isAuthenticating">Connect with Reddit</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Reddit')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Pinterest Modal -->
  <TairoModal :open="modalVisible.Pinterest" size="md" @close="closeModal('Pinterest')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to Pinterest
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your Pinterest account to manage visual content.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your Pinterest account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Create and manage pins</li>
          <li>Organize boards</li>
          <li>Track pin performance</li>
          <li>Schedule visual content</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('Pinterest')"
        >
          <Icon name="logos:pinterest" class="size-4" />
          <span v-if="!isAuthenticating">Connect with Pinterest</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Pinterest')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- TikTok Modal -->
  <TairoModal :open="modalVisible.TikTok" size="md" @close="closeModal('TikTok')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to TikTok
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your TikTok account to share short-form videos.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your TikTok account to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Upload videos</li>
          <li>Track video performance</li>
          <li>View analytics</li>
          <li>Manage comments</li>
        </ul>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('TikTok')"
        >
          <Icon name="logos:tiktok-icon" class="size-4" />
          <span v-if="!isAuthenticating">Connect with TikTok</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('TikTok')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- YouTube Modal -->
  <TairoModal :open="modalVisible.YouTube" size="md" @close="closeModal('YouTube')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect to YouTube
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:info" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Connect your YouTube channel to manage video content.</span>
      </div>

      <div class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connect your YouTube channel to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Upload and manage videos</li>
          <li>Create playlists</li>
          <li>View channel analytics</li>
          <li>Manage comments and community</li>
        </ul>
      </div>

      <div class="flex items-start gap-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-lg">
        <Icon name="lucide:alert-triangle" class="w-5 h-5 text-amber-600 dark:text-amber-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">This uses Google OAuth. You'll need to authorize access to your YouTube channel.</span>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          color="primary"
          :loading="isAuthenticating"
          :disabled="isAuthenticating"
          @click="authenticateWithSocial('YouTube')"
        >
          <Icon name="logos:youtube-icon" class="size-4" />
          <span v-if="!isAuthenticating">Connect with YouTube</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('YouTube')">
          Cancel
        </BaseButton>
      </div>
    </div>
  </TairoModal>

  <!-- Google Calendar Modal -->
  <TairoModal :open="modalVisible['Google Calendar']" size="md" @close="closeModal('Google Calendar')">
    <template #header>
      <BaseHeading as="h3" size="md" weight="medium">
        Connect Google Calendar
      </BaseHeading>
    </template>

    <div class="space-y-4">
      <div class="flex items-start gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <Icon name="lucide:calendar" class="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
        <span class="text-sm">Sync your Google Calendar events with your workspace calendar for seamless scheduling.</span>
      </div>

      <div v-if="!isGoogleCalendarConnected" class="space-y-3">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          Connecting Google Calendar allows you to:
        </BaseParagraph>
        <ul class="space-y-2 text-sm text-muted-600 dark:text-muted-400 list-disc list-inside">
          <li>Import events from your Google Calendar</li>
          <li>Export workspace events to Google Calendar</li>
          <li>Keep both calendars synchronized automatically</li>
          <li>View all events in a unified calendar view</li>
        </ul>
      </div>

      <div v-else class="space-y-4">
        <div class="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
          <Icon name="lucide:check-circle" class="w-5 h-5 text-green-600 dark:text-green-400" />
          <div>
            <p class="text-sm font-medium text-green-800 dark:text-green-200">
              Google Calendar Connected
            </p>
            <p class="text-xs text-green-600 dark:text-green-400">
              Calendar: {{ googleCalendarIntegration?.calendarName }}
            </p>
          </div>
        </div>

        <div v-if="googleCalendarSyncStatus" class="space-y-2">
          <BaseParagraph size="sm" weight="medium">
            Sync Status
          </BaseParagraph>
          <div class="text-xs text-muted-600 dark:text-muted-400 space-y-1">
            <p>Last sync: {{ googleCalendarSyncStatus.lastSync ? new Date(googleCalendarSyncStatus.lastSync).toLocaleString() : 'Never' }}</p>
            <p>Events synced: {{ googleCalendarSyncStatus.eventCount || 0 }}</p>
            <p>Direction: {{ googleCalendarSyncStatus.syncDirection || 'bidirectional' }}</p>
          </div>
        </div>

        <div class="flex gap-2">
          <BaseButton
            variant="soft"
            size="sm"
            :loading="isGoogleCalendarConnecting"
            @click="handleGoogleCalendarSync"
          >
            <Icon name="lucide:refresh-cw" class="w-4 h-4" />
            <span>Sync Now</span>
          </BaseButton>
          <BaseButton
            variant="outline"
            color="danger"
            size="sm"
            @click="handleGoogleCalendarDisconnect"
          >
            <Icon name="lucide:unlink" class="w-4 h-4" />
            <span>Disconnect</span>
          </BaseButton>
        </div>
      </div>

      <div class="flex gap-2 pt-4">
        <BaseButton
          v-if="!isGoogleCalendarConnected"
          color="primary"
          :loading="isGoogleCalendarConnecting"
          :disabled="isGoogleCalendarConnecting"
          @click="handleGoogleCalendarConnect"
        >
          <Icon name="logos:google-calendar" class="size-4" />
          <span v-if="!isGoogleCalendarConnecting">Connect with Google</span>
          <span v-else>Connecting...</span>
        </BaseButton>
        <BaseButton variant="ghost" @click="closeModal('Google Calendar')">
          {{ isGoogleCalendarConnected ? 'Close' : 'Cancel' }}
        </BaseButton>
      </div>
    </div>
  </TairoModal>
</template>
