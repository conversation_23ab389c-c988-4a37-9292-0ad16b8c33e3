import type { SocialProviderConfig } from '../types/integration'

export const socialProviders: Record<string, SocialProviderConfig> = {
  facebook: {
    id: 'facebook',
    name: 'Facebook',
    icon: 'logos:facebook',
    description: 'Connect your Facebook account to manage pages and posts',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
      scopes: ['pages_manage_posts', 'pages_read_engagement', 'pages_read_user_content'],
      responseType: 'code',
      clientIdEnvVar: 'FACEBOOK_APP_ID',
      clientSecretEnvVar: 'FACEBOOK_APP_SECRET',
    },
    features: ['posts', 'pages', 'insights', 'scheduling'],
    postTypes: ['text', 'image', 'video', 'link'],
  },
  gmail: {
    id: 'gmail',
    name: 'Gmail',
    icon: 'logos:google-gmail',
    description: 'Connect your Gmail account for full API access and advanced features',
    category: 'email',
    oauthConfig: {
      authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      scopes: [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify',
      ],
      responseType: 'code',
      clientIdEnvVar: 'GOOGLE_CLIENT_ID',
      clientSecretEnvVar: 'GOOGLE_CLIENT_SECRET',
    },
    features: ['read', 'send', 'labels', 'threads', 'attachments'],
    postTypes: [],
  },
  outlook: {
    id: 'outlook',
    name: 'Outlook',
    icon: 'simple-icons:microsoftoutlook',
    description: 'Connect your Outlook account for full API access and calendar sync',
    category: 'email',
    oauthConfig: {
      authorizationUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
      tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      scopes: [
        'https://graph.microsoft.com/Mail.ReadWrite',
        'https://graph.microsoft.com/Mail.Send',
        'https://graph.microsoft.com/Calendars.ReadWrite',
      ],
      responseType: 'code',
      clientIdEnvVar: 'MICROSOFT_CLIENT_ID',
      clientSecretEnvVar: 'MICROSOFT_CLIENT_SECRET',
    },
    features: ['read', 'send', 'calendar', 'contacts'],
    postTypes: [],
  },
  instagram: {
    id: 'instagram',
    name: 'Instagram',
    icon: 'logos:instagram-icon',
    description: 'Connect your Instagram Business account',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://api.instagram.com/oauth/authorize',
      tokenUrl: 'https://api.instagram.com/oauth/access_token',
      scopes: ['user_profile', 'user_media'],
      responseType: 'code',
      clientIdEnvVar: 'INSTAGRAM_CLIENT_ID',
      clientSecretEnvVar: 'INSTAGRAM_CLIENT_SECRET',
    },
    features: ['posts', 'stories', 'reels', 'insights'],
    postTypes: ['image', 'video', 'carousel'],
  },
  linkedin: {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: 'logos:linkedin-icon',
    description: 'Connect your LinkedIn profile or company page',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
      scopes: ['r_liteprofile', 'r_emailaddress', 'w_member_social'],
      responseType: 'code',
      clientIdEnvVar: 'LINKEDIN_CLIENT_ID',
      clientSecretEnvVar: 'LINKEDIN_CLIENT_SECRET',
    },
    features: ['posts', 'articles', 'company-updates'],
    postTypes: ['text', 'image', 'video', 'article'],
  },
  twitter: {
    id: 'twitter',
    name: 'X (Twitter)',
    icon: 'logos:twitter',
    description: 'Connect your X account to post tweets and threads',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://twitter.com/i/oauth2/authorize',
      tokenUrl: 'https://api.twitter.com/2/oauth2/token',
      scopes: ['tweet.read', 'tweet.write', 'users.read', 'offline.access'],
      responseType: 'code',
      clientIdEnvVar: 'TWITTER_CLIENT_ID',
      clientSecretEnvVar: 'TWITTER_CLIENT_SECRET',
    },
    features: ['tweets', 'threads', 'polls', 'spaces'],
    postTypes: ['text', 'image', 'video', 'poll'],
  },
  reddit: {
    id: 'reddit',
    name: 'Reddit',
    icon: 'logos:reddit-icon',
    description: 'Connect your Reddit account to post and manage content',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.reddit.com/api/v1/authorize',
      tokenUrl: 'https://www.reddit.com/api/v1/access_token',
      scopes: ['identity', 'submit', 'read', 'edit'],
      responseType: 'code',
      clientIdEnvVar: 'REDDIT_CLIENT_ID',
      clientSecretEnvVar: 'REDDIT_CLIENT_SECRET',
    },
    features: ['posts', 'comments', 'subreddits'],
    postTypes: ['text', 'link', 'image', 'video'],
  },
  pinterest: {
    id: 'pinterest',
    name: 'Pinterest',
    icon: 'logos:pinterest',
    description: 'Connect your Pinterest account to manage pins and boards',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.pinterest.com/oauth/',
      tokenUrl: 'https://api.pinterest.com/v5/oauth/token',
      scopes: ['boards:read', 'boards:write', 'pins:read', 'pins:write'],
      responseType: 'code',
      clientIdEnvVar: 'PINTEREST_APP_ID',
      clientSecretEnvVar: 'PINTEREST_APP_SECRET',
    },
    features: ['pins', 'boards', 'analytics'],
    postTypes: ['image', 'video'],
  },
  tiktok: {
    id: 'tiktok',
    name: 'TikTok',
    icon: 'logos:tiktok-icon',
    description: 'Connect your TikTok account to manage videos',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.tiktok.com/auth/authorize/',
      tokenUrl: 'https://open-api.tiktok.com/oauth/access_token/',
      scopes: ['user.info.basic', 'video.list', 'video.upload'],
      responseType: 'code',
      clientIdEnvVar: 'TIKTOK_CLIENT_KEY',
      clientSecretEnvVar: 'TIKTOK_CLIENT_SECRET',
    },
    features: ['videos', 'analytics', 'comments'],
    postTypes: ['video'],
  },
  youtube: {
    id: 'youtube',
    name: 'YouTube',
    icon: 'logos:youtube-icon',
    description: 'Connect your YouTube channel to manage videos and playlists',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      scopes: [
        'https://www.googleapis.com/auth/youtube',
        'https://www.googleapis.com/auth/youtube.upload',
      ],
      responseType: 'code',
      clientIdEnvVar: 'GOOGLE_CLIENT_ID',
      clientSecretEnvVar: 'GOOGLE_CLIENT_SECRET',
    },
    features: ['videos', 'playlists', 'live-streams', 'analytics'],
    postTypes: ['video', 'short'],
  },
  threads: {
    id: 'threads',
    name: 'Threads',
    icon: 'simple-icons:threads',
    description: 'Connect your Threads account to post updates',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://www.threads.net/oauth/authorize',
      tokenUrl: 'https://graph.threads.net/oauth/access_token',
      scopes: ['threads_basic', 'threads_content_publish'],
      responseType: 'code',
      clientIdEnvVar: 'THREADS_APP_ID',
      clientSecretEnvVar: 'THREADS_APP_SECRET',
    },
    features: ['posts', 'replies'],
    postTypes: ['text', 'image'],
  },
  snapchat: {
    id: 'snapchat',
    name: 'Snapchat',
    icon: 'logos:snapchat',
    description: 'Connect your Snapchat account for advertising',
    category: 'social',
    oauthConfig: {
      authorizationUrl: 'https://accounts.snapchat.com/accounts/oauth2/auth',
      tokenUrl: 'https://accounts.snapchat.com/accounts/oauth2/token',
      scopes: ['snapchat-marketing-api'],
      responseType: 'code',
      clientIdEnvVar: 'SNAPCHAT_CLIENT_ID',
      clientSecretEnvVar: 'SNAPCHAT_CLIENT_SECRET',
    },
    features: ['ads', 'analytics'],
    postTypes: ['ad'],
  },
}

export function getSocialProvider(providerId: string): SocialProviderConfig | undefined {
  return socialProviders[providerId]
}

export function getAllSocialProviders(): SocialProviderConfig[] {
  return Object.values(socialProviders)
}
