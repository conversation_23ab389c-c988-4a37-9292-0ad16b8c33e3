import type { EmailProviderConfig } from '../types/integration'

export const emailProviders: EmailProviderConfig[] = [
  {
    id: 'gmail',
    name: 'Gmail',
    icon: 'logos:google-gmail',
    description: 'Connect your Gmail account using OAuth2 for secure access',
    category: 'email',
    authType: 'oauth',
    oauthConfig: {
      authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      scopes: [
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify',
      ],
      clientIdEnvVar: 'GOOGLE_CLIENT_ID',
      clientSecretEnvVar: 'GOOGLE_CLIENT_SECRET',
    },
    features: [
      'OAuth2 Authentication',
      'Full Email Sync',
      'Send Email',
      'Thread Management',
      'Label Support',
      'Search',
      'Attachment Support',
    ],
    supportedFolders: ['INBOX', 'SENT', 'DRAFTS', 'TRASH', 'SPAM', 'IMPORTANT'],
  },
  {
    id: 'outlook',
    name: 'Outlook',
    icon: 'simple-icons:microsoftoutlook',
    description: 'Connect your Outlook/Hotmail account using Microsoft OAuth',
    category: 'email',
    authType: 'oauth',
    oauthConfig: {
      authorizationUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
      tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      scopes: [
        'https://graph.microsoft.com/Mail.Read',
        'https://graph.microsoft.com/Mail.Send',
        'https://graph.microsoft.com/Mail.ReadWrite',
      ],
      clientIdEnvVar: 'MICROSOFT_CLIENT_ID',
      clientSecretEnvVar: 'MICROSOFT_CLIENT_SECRET',
    },
    features: [
      'OAuth2 Authentication',
      'Microsoft Graph API',
      'Full Email Sync',
      'Send Email',
      'Folder Management',
      'Search',
      'Attachment Support',
    ],
    supportedFolders: ['Inbox', 'SentItems', 'Drafts', 'DeletedItems', 'JunkEmail', 'Archive'],
  },
  {
    id: 'yahoo',
    name: 'Yahoo Mail',
    icon: 'logos:yahoo',
    description: 'Connect your Yahoo Mail account using app passwords',
    category: 'email',
    authType: 'app-password',
    serverConfig: {
      imapHost: 'imap.mail.yahoo.com',
      imapPort: 993,
      smtpHost: 'smtp.mail.yahoo.com',
      smtpPort: 587,
      secure: true,
    },
    features: [
      'App Password Authentication',
      'IMAP/SMTP Access',
      'Full Email Sync',
      'Send Email',
      'Folder Support',
      'Attachment Support',
    ],
    supportedFolders: ['INBOX', 'Sent', 'Drafts', 'Trash', 'Spam', 'Archive'],
  },
  {
    id: 'exchange',
    name: 'Exchange Server',
    icon: 'logos:microsoft-exchange',
    description: 'Connect to Microsoft Exchange Server with credentials',
    category: 'email',
    authType: 'password',
    features: [
      'Exchange Web Services',
      'Enterprise Authentication',
      'Full Email Sync',
      'Send Email',
      'Calendar Integration',
      'Contact Sync',
      'Attachment Support',
    ],
    supportedFolders: ['Inbox', 'SentItems', 'Drafts', 'DeletedItems', 'JunkEmail', 'Archive'],
  },
  {
    id: 'imap',
    name: 'IMAP Server',
    icon: 'lucide:mail',
    description: 'Connect to any IMAP/SMTP server with custom settings',
    category: 'email',
    authType: 'password',
    features: [
      'Custom IMAP/SMTP Configuration',
      'Username/Password Authentication',
      'Full Email Sync',
      'Send Email',
      'Custom Folder Support',
      'Attachment Support',
    ],
    supportedFolders: ['INBOX', 'SENT', 'DRAFTS', 'TRASH'],
  },
  {
    id: 'custom',
    name: 'Custom Email Server',
    icon: 'lucide:server',
    description: 'Configure a custom email server with advanced settings',
    category: 'email',
    authType: 'password',
    features: [
      'Fully Customizable',
      'Advanced Configuration',
      'Multiple Auth Methods',
      'Custom Port Configuration',
      'SSL/TLS Support',
      'Custom Folder Mapping',
    ],
    supportedFolders: [],
  },
]

export function getEmailProvider(id: string): EmailProviderConfig | undefined {
  return emailProviders.find(provider => provider.id === id)
}

export function getEmailProvidersByAuthType(authType: 'oauth' | 'password' | 'app-password'): EmailProviderConfig[] {
  return emailProviders.filter(provider => provider.authType === authType)
}

export function getOAuthEmailProviders(): EmailProviderConfig[] {
  return emailProviders.filter(provider => provider.authType === 'oauth')
}

export function getPasswordEmailProviders(): EmailProviderConfig[] {
  return emailProviders.filter(provider => provider.authType === 'password' || provider.authType === 'app-password')
}
