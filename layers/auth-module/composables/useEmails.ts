import type { Unsubscribe } from 'firebase/firestore'
import type { EmailMessage } from '../types/integration'
import {
  collection,
  deleteDoc,
  doc,
  limit as firestoreLimit,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
} from 'firebase/firestore'

export function useEmails(accountId?: MaybeRef<string | null>) {
  const { $clientFirestore } = useNuxtApp()
  const { currentUser } = useAuth()

  // State
  const emails = ref<EmailMessage[]>([])
  const loading = ref(false)
  const syncing = ref(false)
  const error = ref<string | null>(null)
  const hasMore = ref(true)
  const lastDoc = ref<any>(null)

  // Unsubscribe function for cleanup
  let unsubscribe: Unsubscribe | null = null

  // Computed properties
  const unreadEmails = computed(() =>
    emails.value.filter(email => !email.flags?.seen),
  )

  const unreadCount = computed(() => unreadEmails.value.length)

  const readEmails = computed(() =>
    emails.value.filter(email => email.flags?.seen),
  )

  const starredEmails = computed(() =>
    emails.value.filter(email => email.flags?.flagged),
  )

  // Subscribe to emails for specific account or all accounts
  const subscribeToEmails = (specificAccountId?: string, options?: {
    folder?: string
    limit?: number
    unreadOnly?: boolean
  }) => {
    if (!currentUser || !currentUser.value)
      return

    // Clean up existing subscription
    if (unsubscribe && typeof unsubscribe === 'function') {
      unsubscribe()
      unsubscribe = null
    }

    loading.value = true
    error.value = null

    try {
      const emailsRef = collection($clientFirestore, 'email_messages')
      let q = query(
        emailsRef,
        where('userId', '==', currentUser.value.id),
        orderBy('receivedAt', 'desc'),
      )

      // Filter by account if specified
      if (specificAccountId) {
        q = query(q, where('accountId', '==', specificAccountId))
      }

      // Filter by folder if specified
      if (options?.folder) {
        q = query(q, where('folder', '==', options.folder))
      }

      // Filter unread only if specified
      if (options?.unreadOnly) {
        q = query(q, where('flags.seen', '==', false))
      }

      // Apply limit
      if (options?.limit) {
        q = query(q, firestoreLimit(options.limit))
      }

      unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          emails.value = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as EmailMessage[]

          // Update pagination state
          lastDoc.value = snapshot.docs[snapshot.docs.length - 1]
          hasMore.value = snapshot.docs.length === (options?.limit || 50)

          loading.value = false
        },
        (err) => {
          console.error('Error fetching emails:', err)
          error.value = err.message
          loading.value = false
        },
      )
    }
    catch (err) {
      console.error('Error setting up emails subscription:', err)
      error.value = (err as Error).message
      loading.value = false
    }
  }

  // Load more emails (pagination)
  const loadMoreEmails = async (specificAccountId?: string, options?: any) => {
    if (!hasMore.value || loading.value || !lastDoc.value)
      return

    loading.value = true

    try {
      const emailsRef = collection($clientFirestore, 'email_messages')
      let q = query(
        emailsRef,
        where('userId', '==', currentUser.value!.id),
        orderBy('receivedAt', 'desc'),
        startAfter(lastDoc.value),
        firestoreLimit(options?.limit || 25),
      )

      if (specificAccountId) {
        q = query(q, where('accountId', '==', specificAccountId))
      }

      const snapshot = await getDocs(q)
      const moreEmails = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as EmailMessage[]

      emails.value.push(...moreEmails)
      lastDoc.value = snapshot.docs[snapshot.docs.length - 1]
      hasMore.value = snapshot.docs.length === (options?.limit || 25)
    }
    catch (err) {
      console.error('Error loading more emails:', err)
      error.value = (err as Error).message
    }
    finally {
      loading.value = false
    }
  }

  // Mark email as read/unread
  const markEmailAsRead = async (emailId: string, isRead: boolean = true) => {
    try {
      const emailRef = doc($clientFirestore, 'email_messages', emailId)
      await updateDoc(emailRef, {
        'flags.seen': isRead,
        'updatedAt': new Date().toISOString(),
      })

      // Update local state immediately for better UX
      const emailIndex = emails.value.findIndex(email => email.id === emailId)
      if (emailIndex !== -1) {
        if (!emails.value[emailIndex].flags) {
          emails.value[emailIndex].flags = { seen: false, flagged: false, answered: false, deleted: false, draft: false }
        }
        emails.value[emailIndex].flags.seen = isRead
      }
    }
    catch (err) {
      console.error('Error marking email as read:', err)
      throw err
    }
  }

  // Star/unstar email
  const toggleEmailStar = async (emailId: string) => {
    try {
      const email = emails.value.find(e => e.id === emailId)
      if (!email)
        return

      const emailRef = doc($clientFirestore, 'email_messages', emailId)
      const newStarred = !email.flags?.flagged

      await updateDoc(emailRef, {
        'flags.flagged': newStarred,
        'updatedAt': new Date().toISOString(),
      })

      // Update local state
      if (!email.flags) {
        email.flags = { seen: false, flagged: false, answered: false, deleted: false, draft: false }
      }
      email.flags.flagged = newStarred
    }
    catch (err) {
      console.error('Error toggling email star:', err)
      throw err
    }
  }

  // Delete email
  const deleteEmail = async (emailId: string) => {
    try {
      const emailRef = doc($clientFirestore, 'email_messages', emailId)
      await deleteDoc(emailRef)

      // Remove from local state
      emails.value = emails.value.filter(email => email.id !== emailId)
    }
    catch (err) {
      console.error('Error deleting email:', err)
      throw err
    }
  }

  // Send reply
  const sendReply = async (emailId: string, replyData: {
    to: string[]
    cc?: string[]
    bcc?: string[]
    subject: string
    content: {
      text: string
      html?: string
    }
    attachments?: any[]
  }) => {
    try {
      const originalEmail = emails.value.find(e => e.id === emailId)
      if (!originalEmail)
        throw new Error('Original email not found')

      const result = await $fetch('/api/integrations/email/send', {
        method: 'POST',
        body: {
          accountId: originalEmail.accountId,
          message: {
            ...replyData,
            inReplyTo: originalEmail.messageId,
            references: [originalEmail.messageId, ...(originalEmail.references || [])],
          },
        },
      })

      return result
    }
    catch (err) {
      console.error('Error sending reply:', err)
      throw err
    }
  }

  // Sync emails for account
  const syncEmails = async (accountId: string) => {
    syncing.value = true

    try {
      const result = await $fetch('/api/integrations/email/sync', {
        method: 'POST',
        body: {
          accountId,
          options: {
            limit: 50,
            includeAttachments: true,
          },
        },
      })

      return result
    }
    catch (err) {
      console.error('Error syncing emails:', err)
      throw err
    }
    finally {
      syncing.value = false
    }
  }

  // Search emails
  const searchEmails = (query: string) => {
    if (!query.trim())
      return emails.value

    const searchTerm = query.toLowerCase()
    return computed(() =>
      emails.value.filter(email =>
        email.subject.toLowerCase().includes(searchTerm)
        || (email.from.name || '').toLowerCase().includes(searchTerm)
        || email.from.email.toLowerCase().includes(searchTerm)
        || (email.body?.text || '').toLowerCase().includes(searchTerm),
      ),
    )
  }

  // Filter emails by criteria
  const filterEmails = (criteria: {
    isRead?: boolean
    isStarred?: boolean
    folder?: string
    sender?: string
    dateRange?: { start: Date, end: Date }
  }) => {
    return computed(() => {
      let filtered = emails.value

      if (criteria.isRead !== undefined) {
        filtered = filtered.filter(email => email.flags?.seen === criteria.isRead)
      }

      if (criteria.isStarred !== undefined) {
        filtered = filtered.filter(email => email.flags?.flagged === criteria.isStarred)
      }

      if (criteria.folder) {
        filtered = filtered.filter(email => email.folder === criteria.folder)
      }

      if (criteria.sender) {
        const senderTerm = criteria.sender.toLowerCase()
        filtered = filtered.filter(email =>
          email.from.email.toLowerCase().includes(senderTerm)
          || (email.from.name || '').toLowerCase().includes(senderTerm),
        )
      }

      if (criteria.dateRange) {
        filtered = filtered.filter((email) => {
          const emailDate = new Date(email.date)
          return emailDate >= criteria.dateRange!.start && emailDate <= criteria.dateRange!.end
        })
      }

      return filtered
    })
  }

  // Get email by ID
  const getEmailById = (emailId: string) => {
    return computed(() => emails.value.find(email => email.id === emailId))
  }

  // Lifecycle
  onMounted(() => {
    if (currentUser && currentUser.value) {
      const currentAccountId = unref(accountId)
      if (currentAccountId) {
        subscribeToEmails(currentAccountId)
      }
    }
  })

  onUnmounted(() => {
    if (unsubscribe && typeof unsubscribe === 'function') {
      unsubscribe()
      unsubscribe = null
    }
  })

  // Watch for user changes
  watch(() => currentUser?.value, (newUser) => {
    if (newUser) {
      const currentAccountId = unref(accountId)
      if (currentAccountId) {
        subscribeToEmails(currentAccountId)
      }
    }
    else {
      emails.value = []
      if (unsubscribe && typeof unsubscribe === 'function') {
        unsubscribe()
        unsubscribe = null
      }
    }
  }, { immediate: true })

  // Watch for account ID changes
  watch(() => unref(accountId), (newAccountId) => {
    if (currentUser.value && newAccountId) {
      subscribeToEmails(newAccountId)
    }
  })

  return {
    emails: readonly(emails),
    loading: readonly(loading),
    syncing: readonly(syncing),
    error: readonly(error),
    hasMore: readonly(hasMore),
    unreadEmails,
    unreadCount,
    readEmails,
    starredEmails,
    subscribeToEmails,
    loadMoreEmails,
    markEmailAsRead,
    toggleEmailStar,
    deleteEmail,
    sendReply,
    syncEmails,
    searchEmails,
    filterEmails,
    getEmailById,
  }
}
