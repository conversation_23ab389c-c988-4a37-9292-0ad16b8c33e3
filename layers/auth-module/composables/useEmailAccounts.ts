import type { Timestamp, Unsubscribe } from 'firebase/firestore'
import type { EmailAccount } from '../types/integration'
import {
  addDoc,
  collection,
  doc,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  updateDoc,
  where,
} from 'firebase/firestore'

export function useEmailAccounts() {
  const { $clientFirestore } = useNuxtApp()
  const { currentUser, currentWorkspace } = useAuth()

  // State
  const emailAccounts = ref<EmailAccount[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Unsubscribe function for cleanup
  let unsubscribe: Unsubscribe | null = null

  // Subscribe to email accounts
  const subscribeToAccounts = () => {
    if (!currentUser || !currentUser.value)
      return

    loading.value = true
    error.value = null

    try {
      const accountsRef = collection($clientFirestore, 'email_accounts')
      const q = query(
        accountsRef,
        where('userId', '==', currentUser.value.id),
        orderBy('createdAt', 'desc'),
      )

      unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          emailAccounts.value = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
          })) as EmailAccount[]
          loading.value = false
        },
        (err) => {
          console.error('Error fetching email accounts:', err)
          error.value = err.message
          loading.value = false
        },
      )
    }
    catch (err) {
      console.error('Error setting up email accounts subscription:', err)
      error.value = (err as Error).message
      loading.value = false
    }
  }

  // Create a new email account
  const createEmailAccount = async (accountData: Omit<EmailAccount, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!currentUser.value) {
      throw new Error('User not authenticated')
    }

    try {
      loading.value = true
      error.value = null

      const accountsRef = collection($clientFirestore, 'email_accounts')

      const newAccount = {
        ...accountData,
        userId: currentUser.value.id,
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
      }

      const docRef = await addDoc(accountsRef, newAccount)
      return { id: docRef.id, ...newAccount }
    }
    catch (err) {
      error.value = (err as Error).message
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // Update an email account
  const updateEmailAccount = async (id: string, updates: Partial<EmailAccount>) => {
    try {
      loading.value = true
      error.value = null

      const docRef = doc($clientFirestore, 'email_accounts', id)

      // Remove fields that shouldn't be updated
      const { id: _, userId, createdAt, ...safeUpdates } = updates

      await updateDoc(docRef, {
        ...safeUpdates,
        updatedAt: serverTimestamp(),
      })
    }
    catch (err) {
      error.value = (err as Error).message
      throw err
    }
    finally {
      loading.value = false
    }
  }

  // Sync emails for an account
  const syncEmailAccount = async (accountId: string) => {
    const account = emailAccounts.value.find(acc => acc.id === accountId)
    if (!account) {
      throw new Error('Email account not found')
    }

    try {
      // Update sync status
      await updateEmailAccount(accountId, { syncStatus: 'syncing' })

      // Call sync API
      const syncResult = await $fetch('/api/integrations/email/sync', {
        method: 'POST',
        body: {
          accountId,
          provider: account.provider,
          email: account.email,
          // Note: In real implementation, credentials would be decrypted
          password: 'encrypted_password_placeholder',
          syncFolders: ['INBOX', 'SENT'],
          maxEmails: 100,
        },
      })

      // Update sync status and timestamp
      await updateEmailAccount(accountId, {
        syncStatus: 'idle',
        lastSyncAt: serverTimestamp() as Timestamp,
        unreadCount: syncResult.syncResults.newMessages || 0,
      })

      return syncResult
    }
    catch (err) {
      // Update sync status on error
      await updateEmailAccount(accountId, { syncStatus: 'error' })
      throw err
    }
  }

  // Get account by ID
  const getEmailAccount = (accountId: string) => {
    return computed(() => emailAccounts.value.find(acc => acc.id === accountId))
  }

  // Get default account
  const getDefaultEmailAccount = () => {
    return computed(() => emailAccounts.value.find(acc => acc.isDefault))
  }

  // Get accounts by provider
  const getEmailAccountsByProvider = (provider: string) => {
    return computed(() => emailAccounts.value.filter(acc => acc.provider === provider))
  }

  // Set default account
  const setDefaultEmailAccount = async (accountId: string) => {
    try {
      loading.value = true

      // Unset any existing default
      const currentDefaults = emailAccounts.value.filter(acc => acc.isDefault && acc.id !== accountId)

      for (const account of currentDefaults) {
        await updateEmailAccount(account.id, { isDefault: false })
      }

      // Set new default
      await updateEmailAccount(accountId, { isDefault: true })
    }
    finally {
      loading.value = false
    }
  }

  // Lifecycle
  onMounted(() => {
    if (currentUser && currentUser.value) {
      subscribeToAccounts()
    }
  })

  onUnmounted(() => {
    if (unsubscribe && typeof unsubscribe === 'function') {
      unsubscribe()
      unsubscribe = null
    }
  })

  // Watch for user changes
  watch(() => currentUser?.value, (newUser) => {
    if (newUser) {
      subscribeToAccounts()
    }
    else {
      emailAccounts.value = []
      if (unsubscribe && typeof unsubscribe === 'function') {
        unsubscribe()
        unsubscribe = null
      }
    }
  }, { immediate: true })

  return {
    emailAccounts: readonly(emailAccounts),
    loading: readonly(loading),
    error: readonly(error),
    createEmailAccount,
    updateEmailAccount,
    syncEmailAccount,
    getEmailAccount,
    getDefaultEmailAccount,
    getEmailAccountsByProvider,
    setDefaultEmailAccount,
  }
}
