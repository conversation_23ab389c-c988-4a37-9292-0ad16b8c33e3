import type {
  CalendarIntegration,
  GoogleCalendarConfig,
} from '~/utils/bundles/calendar/types'

// Get runtime configuration for client-side access
function getGoogleCalendarConfig(): GoogleCalendarConfig {
  const config = useRuntimeConfig()

  return {
    clientId: config.public.oauth.google.clientId || '',
    clientSecret: '', // Not available on client-side (and not needed)
    redirectUri: `${config.public.oauth.google.baseUrl}/api/integrations/google-calendar/callback`,
    scope: [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
    ],
  }
}

export function useGoogleCalendar() {
  const { $firestore } = useNuxtApp()
  const { currentProfile } = useAuth()
  const toaster = useToast()

  const isConnecting = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const integrations = ref<CalendarIntegration[]>([])

  // Get Google Calendar integration for current user
  const currentIntegration = computed(() =>
    integrations.value.find(integration => integration.provider === 'google'),
  )

  // Check if Google Calendar is connected
  const isConnected = computed(() => !!currentIntegration.value?.isActive)

  // Initiate Google Calendar OAuth flow
  async function connectGoogleCalendar() {
    if (!currentProfile.value?.userId || !currentProfile.value?.workspaceId) {
      throw new Error('User not authenticated')
    }

    isConnecting.value = true
    error.value = null

    try {
      // Initialize OAuth flow using the new endpoint
      const redirectUri = `${window.location.origin}/api/integrations/google-calendar/callback`
      const response = await $fetch('/api/integrations/google-calendar/oauth/init', {
        method: 'POST',
        body: { redirectUri },
      })

      const authUrl = response.authUrl

      // Open OAuth popup
      const popup = window.open(
        authUrl,
        'google-calendar-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes',
      )

      // Wait for popup to complete OAuth flow
      return new Promise<void>((resolve, reject) => {
        // Listen for OAuth completion via postMessage
        const messageHandler = async (event: MessageEvent) => {
          if (event.data?.type === 'oauth-success' && event.data?.provider === 'google-calendar') {
            window.removeEventListener('message', messageHandler)
            clearInterval(checkClosed)
            isConnecting.value = false

            try {
              // Check OAuth status to get the integration data
              const statusResponse = await $fetch('/api/integrations/google-calendar/status')

              if (statusResponse.authenticated) {
                // Save the integration to Firestore
                await saveIntegrationToFirestore(statusResponse)

                // Reload integrations to get the new connection
                await loadIntegrations()

                toaster.show({
                  title: 'Google Calendar Connected',
                  message: 'Your Google Calendar has been successfully connected.',
                  color: 'success',
                  icon: 'lucide:check-circle',
                })
                resolve()
              } else {
                throw new Error('OAuth authentication failed')
              }
            } catch (error) {
              console.error('Failed to save Google Calendar integration:', error)
              toaster.show({
                title: 'Connection Failed',
                message: 'Failed to save Google Calendar integration.',
                color: 'danger',
                icon: 'lucide:x-circle',
              })
              reject(error)
            }
          }
          else if (event.data?.type === 'oauth-error' && event.data?.provider === 'google-calendar') {
            window.removeEventListener('message', messageHandler)
            clearInterval(checkClosed)
            isConnecting.value = false

            const errorMessage = event.data.error || 'Google Calendar connection failed'
            toaster.show({
              title: 'Connection Failed',
              message: errorMessage,
              color: 'danger',
              icon: 'lucide:x-circle',
            })
            reject(new Error(errorMessage))
          }
        }

        window.addEventListener('message', messageHandler)

        // Also check if window was closed manually
        const checkClosed = setInterval(() => {
          if (popup?.closed) {
            clearInterval(checkClosed)
            window.removeEventListener('message', messageHandler)
            isConnecting.value = false
            reject(new Error('Google Calendar connection was cancelled'))
          }
        }, 1000)

        // Timeout after 5 minutes
        setTimeout(() => {
          clearInterval(checkClosed)
          window.removeEventListener('message', messageHandler)
          if (popup && !popup.closed) {
            popup.close()
          }
          isConnecting.value = false
          reject(new Error('Google Calendar connection timed out'))
        }, 300000)
      })
    }
    catch (err) {
      isConnecting.value = false
      error.value = `Failed to connect Google Calendar: ${err.message}`
      throw err
    }
  }

  // Disconnect Google Calendar
  async function disconnectGoogleCalendar() {
    if (!currentIntegration.value)
      return

    try {
      const response = await $fetch('/api/integrations/google-calendar/disconnect', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
        },
      })

      if (response.success) {
        await loadIntegrations()
        toaster.show({
          title: 'Google Calendar Disconnected',
          message: 'Your Google Calendar has been disconnected.',
          color: 'info',
          icon: 'lucide:unlink',
        })
      }
    }
    catch (err) {
      error.value = `Failed to disconnect Google Calendar: ${err.message}`
      throw err
    }
  }

  // Save integration data to Firestore
  const saveIntegrationToFirestore = async (statusData: any) => {
    if (!currentProfile.value) {
      throw new Error('No current profile available')
    }

    const integrationData = {
      userId: statusData.userId || currentProfile.value.userId,
      workspaceId: statusData.workspaceId || currentProfile.value.workspaceId,
      provider: 'google-calendar',
      credentials: {
        accessToken: statusData.tokenData.accessToken,
        refreshToken: statusData.tokenData.refreshToken,
        expiryDate: statusData.tokenData.expiresAt ? new Date(statusData.tokenData.expiresAt).getTime() : Date.now() + 3600000,
        tokenType: statusData.tokenData.tokenType || 'Bearer',
      },
      calendarId: statusData.calendar.id,
      calendarName: statusData.calendar.name,
      profile: statusData.profile,
      calendars: statusData.calendars,
      isActive: true,
      syncStatus: {
        lastSync: new Date(),
        isActive: true,
        syncDirection: 'bidirectional',
        eventCount: 0,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Save to Firestore
    await $firestore.collection('calendar_integrations').add(integrationData)
  }

  // Load calendar integrations for current user
  async function loadIntegrations() {
    if (!currentProfile.value?.workspaceId)
      return

    isLoading.value = true
    try {
      const response = await $fetch('/api/integrations/calendar', {
        params: {
          workspaceId: currentProfile.value.workspaceId,
        },
      })
      integrations.value = response.integrations || []
    }
    catch (err) {
      error.value = `Failed to load calendar integrations: ${err.message}`
      console.error('Failed to load calendar integrations:', err)
    }
    finally {
      isLoading.value = false
    }
  }

  // Sync events from Google Calendar
  async function syncFromGoogle() {
    if (!currentIntegration.value) {
      throw new Error('Google Calendar not connected')
    }

    try {
      const response = await $fetch('/api/integrations/google-calendar/sync', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
          direction: 'import',
        },
      })

      toaster.show({
        title: 'Calendar Synced',
        message: `Imported ${response.eventCount} events from Google Calendar.`,
        color: 'success',
        icon: 'lucide:sync',
      })

      return response
    }
    catch (err) {
      error.value = `Failed to sync from Google Calendar: ${err.message}`
      throw err
    }
  }

  // Export events to Google Calendar
  async function syncToGoogle(eventIds?: string[]) {
    if (!currentIntegration.value) {
      throw new Error('Google Calendar not connected')
    }

    try {
      const response = await $fetch('/api/integrations/google-calendar/sync', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
          direction: 'export',
          eventIds,
        },
      })

      toaster.show({
        title: 'Events Exported',
        message: `Exported ${response.eventCount} events to Google Calendar.`,
        color: 'success',
        icon: 'lucide:upload',
      })

      return response
    }
    catch (err) {
      error.value = `Failed to export to Google Calendar: ${err.message}`
      throw err
    }
  }

  // Get sync status
  const syncStatus = computed(() => {
    if (!currentIntegration.value)
      return null
    return currentIntegration.value.syncStatus
  })

  // Load integrations when profile changes
  watch(() => currentProfile.value, (newProfile) => {
    if (newProfile) {
      loadIntegrations()
    }
  }, { immediate: true })

  return {
    // State
    isConnecting,
    isLoading,
    error,
    integrations,
    currentIntegration,
    isConnected,
    syncStatus,

    // Actions
    connectGoogleCalendar,
    disconnectGoogleCalendar,
    loadIntegrations,
    syncFromGoogle,
    syncToGoogle,
  }
}
