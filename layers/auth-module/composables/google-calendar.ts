import type {
  CalendarIntegration,
  GoogleCalendarConfig,
} from '~/utils/bundles/calendar/types'

// Default Google Calendar configuration
const GOOGLE_CALENDAR_CONFIG: GoogleCalendarConfig = {
  clientId: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  redirectUri: `${process.env.NUXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/integrations/google-calendar/callback`,
  scope: [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
  ],
}

export function useGoogleCalendar() {
  const { $firestore } = useNuxtApp()
  const { currentProfile } = useAuth()
  const toaster = useToast()

  const isConnecting = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const integrations = ref<CalendarIntegration[]>([])

  // Get Google Calendar integration for current user
  const currentIntegration = computed(() =>
    integrations.value.find(integration => integration.provider === 'google'),
  )

  // Check if Google Calendar is connected
  const isConnected = computed(() => !!currentIntegration.value?.isActive)

  // Initiate Google Calendar OAuth flow
  async function connectGoogleCalendar() {
    if (!currentProfile.value?.userId || !currentProfile.value?.workspaceId) {
      throw new Error('User not authenticated')
    }

    isConnecting.value = true
    error.value = null

    try {
      // Create OAuth URL
      const params = new URLSearchParams({
        client_id: GOOGLE_CALENDAR_CONFIG.clientId,
        redirect_uri: GOOGLE_CALENDAR_CONFIG.redirectUri,
        scope: GOOGLE_CALENDAR_CONFIG.scope.join(' '),
        response_type: 'code',
        access_type: 'offline',
        prompt: 'consent',
        state: JSON.stringify({
          userId: currentProfile.value.userId,
          workspaceId: currentProfile.value.workspaceId,
          returnUrl: window.location.href,
        }),
      })

      const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params}`

      // Open OAuth popup
      const popup = window.open(
        authUrl,
        'google-calendar-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes',
      )

      // Wait for popup to close
      return new Promise<void>((resolve, reject) => {
        const checkClosed = setInterval(() => {
          if (popup?.closed) {
            clearInterval(checkClosed)
            isConnecting.value = false

            // Check if integration was successful
            setTimeout(() => {
              loadIntegrations().then(() => {
                if (isConnected.value) {
                  toaster.show({
                    title: 'Google Calendar Connected',
                    message: 'Your Google Calendar has been successfully connected.',
                    color: 'success',
                    icon: 'lucide:check-circle',
                  })
                  resolve()
                }
                else {
                  reject(new Error('Google Calendar connection was cancelled or failed'))
                }
              })
            }, 1000)
          }
        }, 1000)

        // Timeout after 5 minutes
        setTimeout(() => {
          clearInterval(checkClosed)
          if (popup && !popup.closed) {
            popup.close()
          }
          isConnecting.value = false
          reject(new Error('Google Calendar connection timed out'))
        }, 300000)
      })
    }
    catch (err) {
      isConnecting.value = false
      error.value = `Failed to connect Google Calendar: ${err.message}`
      throw err
    }
  }

  // Disconnect Google Calendar
  async function disconnectGoogleCalendar() {
    if (!currentIntegration.value)
      return

    try {
      const response = await $fetch('/api/integrations/google-calendar/disconnect', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
        },
      })

      if (response.success) {
        await loadIntegrations()
        toaster.show({
          title: 'Google Calendar Disconnected',
          message: 'Your Google Calendar has been disconnected.',
          color: 'info',
          icon: 'lucide:unlink',
        })
      }
    }
    catch (err) {
      error.value = `Failed to disconnect Google Calendar: ${err.message}`
      throw err
    }
  }

  // Load calendar integrations for current user
  async function loadIntegrations() {
    if (!currentProfile.value?.workspaceId)
      return

    isLoading.value = true
    try {
      const response = await $fetch('/api/integrations/calendar', {
        params: {
          workspaceId: currentProfile.value.workspaceId,
        },
      })
      integrations.value = response.integrations || []
    }
    catch (err) {
      error.value = `Failed to load calendar integrations: ${err.message}`
      console.error('Failed to load calendar integrations:', err)
    }
    finally {
      isLoading.value = false
    }
  }

  // Sync events from Google Calendar
  async function syncFromGoogle() {
    if (!currentIntegration.value) {
      throw new Error('Google Calendar not connected')
    }

    try {
      const response = await $fetch('/api/integrations/google-calendar/sync', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
          direction: 'import',
        },
      })

      toaster.show({
        title: 'Calendar Synced',
        message: `Imported ${response.eventCount} events from Google Calendar.`,
        color: 'success',
        icon: 'lucide:sync',
      })

      return response
    }
    catch (err) {
      error.value = `Failed to sync from Google Calendar: ${err.message}`
      throw err
    }
  }

  // Export events to Google Calendar
  async function syncToGoogle(eventIds?: string[]) {
    if (!currentIntegration.value) {
      throw new Error('Google Calendar not connected')
    }

    try {
      const response = await $fetch('/api/integrations/google-calendar/sync', {
        method: 'POST',
        body: {
          integrationId: currentIntegration.value.id,
          direction: 'export',
          eventIds,
        },
      })

      toaster.show({
        title: 'Events Exported',
        message: `Exported ${response.eventCount} events to Google Calendar.`,
        color: 'success',
        icon: 'lucide:upload',
      })

      return response
    }
    catch (err) {
      error.value = `Failed to export to Google Calendar: ${err.message}`
      throw err
    }
  }

  // Get sync status
  const syncStatus = computed(() => {
    if (!currentIntegration.value)
      return null
    return currentIntegration.value.syncStatus
  })

  // Load integrations when profile changes
  watch(() => currentProfile.value, (newProfile) => {
    if (newProfile) {
      loadIntegrations()
    }
  }, { immediate: true })

  return {
    // State
    isConnecting,
    isLoading,
    error,
    integrations,
    currentIntegration,
    isConnected,
    syncStatus,

    // Actions
    connectGoogleCalendar,
    disconnectGoogleCalendar,
    loadIntegrations,
    syncFromGoogle,
    syncToGoogle,
  }
}
