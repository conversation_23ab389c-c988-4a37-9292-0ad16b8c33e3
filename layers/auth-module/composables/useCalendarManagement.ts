import type { Unsubscribe } from 'firebase/firestore'
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  onSnapshot,
  query,
  serverTimestamp,

  updateDoc,
  where,
} from 'firebase/firestore'
import { computed, ref } from 'vue'

export interface Calendar {
  id: string
  name: string
  description?: string
  color: string
  isDefault: boolean
  isVisible: boolean
  userId: string
  workspaceId: string

  // Integration details
  integrationId?: string
  externalCalendarId?: string
  provider?: 'google' | 'outlook' | 'apple' | null

  // Metadata
  createdAt: Date
  updatedAt: Date
}

export interface CalendarCreationData {
  name: string
  description?: string
  color: string
  isDefault?: boolean
}

export function useCalendarManagement() {
  const { $clientFirestore } = useNuxtApp()
  const { currentUser, currentWorkspace } = useAuth()
  const toaster = useToast()

  // State
  const calendars = ref<Calendar[]>([])
  const selectedCalendarId = ref<string>('all')
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Real-time subscription
  const unsubscribe = ref<Unsubscribe | null>(null)

  // Calendar colors for selection
  const calendarColors = [
    { name: 'Blue', value: '#3B82F6', bg: 'bg-blue-500' },
    { name: 'Green', value: '#10B981', bg: 'bg-green-500' },
    { name: 'Purple', value: '#8B5CF6', bg: 'bg-purple-500' },
    { name: 'Pink', value: '#EC4899', bg: 'bg-pink-500' },
    { name: 'Orange', value: '#F59E0B', bg: 'bg-orange-500' },
    { name: 'Red', value: '#EF4444', bg: 'bg-red-500' },
    { name: 'Indigo', value: '#6366F1', bg: 'bg-indigo-500' },
    { name: 'Teal', value: '#14B8A6', bg: 'bg-teal-500' },
  ]

  // Computed properties
  const selectedCalendar = computed(() => {
    if (selectedCalendarId.value === 'all')
      return null
    return calendars.value.find(cal => cal.id === selectedCalendarId.value)
  })

  const visibleCalendars = computed(() =>
    calendars.value.filter(cal => cal.isVisible),
  )

  const defaultCalendar = computed(() =>
    calendars.value.find(cal => cal.isDefault),
  )

  // Subscribe to calendars
  async function subscribeToCalendars() {
    if (!currentUser.value || !currentWorkspace.value)
      return

    loading.value = true

    try {
      const calendarsRef = collection($clientFirestore, 'calendars')
      const q = query(
        calendarsRef,
        where('workspaceId', '==', currentWorkspace.value.id),
        where('userId', '==', currentUser.value.id),
      )

      unsubscribe.value = onSnapshot(
        q,
        (snapshot) => {
          calendars.value = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate(),
            updatedAt: doc.data().updatedAt?.toDate(),
          })) as Calendar[]

          // Set default selection if none selected
          if (selectedCalendarId.value === '' && calendars.value.length > 0) {
            const defaultCal = calendars.value.find(cal => cal.isDefault)
            selectedCalendarId.value = defaultCal?.id || 'all'
          }

          loading.value = false
        },
        (err) => {
          console.error('Error fetching calendars:', err)
          error.value = err.message
          loading.value = false
        },
      )
    }
    catch (err: any) {
      console.error('Error subscribing to calendars:', err)
      error.value = err.message
      loading.value = false
    }
  }

  // Create new calendar
  async function createCalendar(data: CalendarCreationData): Promise<Calendar> {
    if (!currentUser.value || !currentWorkspace.value) {
      throw new Error('Authentication required')
    }

    try {
      const newCalendar = {
        name: data.name,
        description: data.description || '',
        color: data.color,
        isDefault: data.isDefault || false,
        isVisible: true,
        userId: currentUser.value.id,
        workspaceId: currentWorkspace.value.id,
        provider: null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      // If this is set as default, update existing default
      if (data.isDefault) {
        await updateDefaultCalendar(null) // Clear existing default
      }

      const docRef = await addDoc(collection($clientFirestore, 'calendars'), newCalendar)

      toaster.add({
        title: 'Calendar Created',
        description: `Calendar "${data.name}" has been created successfully`,
        color: 'green',
        icon: 'heroicons:check-circle',
      })

      return {
        id: docRef.id,
        ...newCalendar,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as Calendar
    }
    catch (error: any) {
      console.error('Error creating calendar:', error)
      toaster.add({
        title: 'Error Creating Calendar',
        description: error.message,
        color: 'red',
        icon: 'heroicons:x-circle',
      })
      throw error
    }
  }

  // Update calendar
  async function updateCalendar(calendarId: string, updates: Partial<Calendar>): Promise<void> {
    try {
      const calendarRef = doc($clientFirestore, 'calendars', calendarId)

      await updateDoc(calendarRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      })

      toaster.add({
        title: 'Calendar Updated',
        description: 'Calendar has been updated successfully',
        color: 'green',
        icon: 'heroicons:check-circle',
      })
    }
    catch (error: any) {
      console.error('Error updating calendar:', error)
      toaster.add({
        title: 'Error Updating Calendar',
        description: error.message,
        color: 'red',
        icon: 'heroicons:x-circle',
      })
      throw error
    }
  }

  // Delete calendar
  async function deleteCalendar(calendarId: string): Promise<void> {
    try {
      await deleteDoc(doc($clientFirestore, 'calendars', calendarId))

      // If deleted calendar was selected, reset to 'all'
      if (selectedCalendarId.value === calendarId) {
        selectedCalendarId.value = 'all'
      }

      toaster.add({
        title: 'Calendar Deleted',
        description: 'Calendar has been deleted successfully',
        color: 'green',
        icon: 'heroicons:check-circle',
      })
    }
    catch (error: any) {
      console.error('Error deleting calendar:', error)
      toaster.add({
        title: 'Error Deleting Calendar',
        description: error.message,
        color: 'red',
        icon: 'heroicons:x-circle',
      })
      throw error
    }
  }

  // Toggle calendar visibility
  async function toggleCalendarVisibility(calendarId: string): Promise<void> {
    const calendar = calendars.value.find(cal => cal.id === calendarId)
    if (!calendar)
      return

    await updateCalendar(calendarId, { isVisible: !calendar.isVisible })
  }

  // Set default calendar
  async function updateDefaultCalendar(calendarId: string | null): Promise<void> {
    // Update all calendars to not be default
    const updatePromises = calendars.value.map((calendar) => {
      if (calendar.isDefault) {
        return updateDoc(doc($clientFirestore, 'calendars', calendar.id), {
          isDefault: false,
          updatedAt: serverTimestamp(),
        })
      }
    })

    await Promise.all(updatePromises.filter(Boolean))

    // Set new default if provided
    if (calendarId) {
      await updateDoc(doc($clientFirestore, 'calendars', calendarId), {
        isDefault: true,
        updatedAt: serverTimestamp(),
      })
    }
  }

  // Create calendar from integration
  async function createCalendarFromIntegration(
    name: string,
    integrationId: string,
    externalCalendarId: string,
    provider: 'google' | 'outlook' | 'apple',
    color: string = '#3B82F6',
  ): Promise<Calendar> {
    return await createCalendar({
      name,
      color,
      isDefault: false,
    }).then(async (calendar) => {
      // Link to integration
      await updateCalendar(calendar.id, {
        integrationId,
        externalCalendarId,
        provider,
      })
      return calendar
    })
  }

  // Cleanup subscription
  function cleanup() {
    unsubscribe.value?.()
  }

  // Auto-cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    calendars: readonly(calendars),
    selectedCalendarId,
    selectedCalendar,
    visibleCalendars,
    defaultCalendar,
    loading: readonly(loading),
    error: readonly(error),
    calendarColors,

    // Methods
    subscribeToCalendars,
    createCalendar,
    updateCalendar,
    deleteCalendar,
    toggleCalendarVisibility,
    updateDefaultCalendar,
    createCalendarFromIntegration,
    cleanup,
  }
}
