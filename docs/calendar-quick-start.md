# Calendar System Quick Start Guide

## 🚀 Getting Started

This guide will help you quickly set up and start using the Calendar Integration System in your PIB application.

---

## 📋 Prerequisites

Before you begin, ensure you have:

- **Node.js 20+** installed
- **pnpm** package manager
- **Firebase project** set up
- **Google Cloud Console** account (for Google Calendar integration)
- **PIB application** running locally

---

## ⚡ 5-Minute Setup

### Step 1: Install Dependencies

```bash
# Install required packages
pnpm install googleapis

# Verify installation
pnpm list googleapis
```

### Step 2: Configure Environment Variables

Create or update your `.env` file:

```bash
# Firebase Configuration (if not already set)
NUXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NUXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id

# Google Calendar API
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Application URL
NUXT_PUBLIC_BASE_URL=http://localhost:3000
```

### Step 3: Deploy Firestore Rules

```bash
# Deploy security rules for calendar collections
pnpm deploy:rules

# Verify deployment
firebase firestore:rules:get
```

### Step 4: Start Development Server

```bash
# Start the application
pnpm dev

# Open calendar page
open http://localhost:3000/calendar
```

### Step 5: Test Basic Functionality

1. **Navigate to Calendar**: Go to `/calendar` page
2. **Create Event**: Double-click on calendar to create an event
3. **Edit Event**: Click on an event to edit details
4. **Verify Persistence**: Refresh page to see events persist

---

## 🎯 Basic Usage

### Creating Your First Event

```vue
<script setup>
import { useCalendarEvents } from '~/utils/bundles/calendar'

const { createEvent } = useCalendarEvents({
  fromDate: ref(new Date()),
  toDate: ref(new Date())
})

// Create a simple event
async function createTeamMeeting() {
  const event = await createEvent({
    title: 'Team Standup',
    startDate: new Date('2024-01-15T09:00:00'),
    endDate: new Date('2024-01-15T09:30:00'),
    category: 'team',
    description: 'Daily team standup meeting'
  })

  console.log('Created event:', event.id)
}
</script>
```

### Reading Calendar Events

```vue
<script setup>
const { fromDate, toDate } = useDateRange()
const { calendarEvents, loading } = useCalendarEvents({
  fromDate,
  toDate
})

// Events automatically update in real-time
watch(calendarEvents, (events) => {
  console.log(`Found ${events.length} events`)
})
</script>

<template>
  <div v-if="loading">
    Loading events...
  </div>
  <div v-else>
    <div v-for="event in calendarEvents" :key="event.key">
      {{ event.customData.title }}
    </div>
  </div>
</template>
```

### Updating Calendar Settings

```vue
<script setup>
const { settings, updateSettings } = useCalendarSettings()

// Customize calendar view
async function setupCalendar() {
  await updateSettings({
    hideWeekends: false,
    hourOpen: 8,
    hourClose: 18,
    weekStartsOn: 1, // Monday
    hourPrecision: 15 // 15-minute intervals
  })
}
</script>
```

---

## 🔗 Google Calendar Integration

### Step 1: Set Up Google OAuth

1. **Go to [Google Cloud Console](https://console.cloud.google.com)**
2. **Create or select a project**
3. **Enable Google Calendar API**
4. **Create OAuth 2.0 credentials**
5. **Add authorized redirect URIs**:
   - `http://localhost:3000/api/integrations/google-calendar/callback`
   - `https://your-domain.com/api/integrations/google-calendar/callback`

### Step 2: Connect Google Calendar

```vue
<script setup>
import { useGoogleCalendar } from '~/composables/google-calendar'

const { isConnected, connectToGoogle, syncCalendar } = useGoogleCalendar()

// Connect to Google Calendar
async function handleConnect() {
  try {
    await connectToGoogle()
    console.log('Connected to Google Calendar!')
  }
  catch (error) {
    console.error('Connection failed:', error)
  }
}

// Sync events
async function handleSync() {
  const result = await syncCalendar('bidirectional')
  console.log(`Synced ${result.eventsProcessed} events`)
}
</script>

<template>
  <div>
    <button v-if="!isConnected" @click="handleConnect">
      Connect Google Calendar
    </button>
    <button v-else @click="handleSync">
      Sync Calendar
    </button>
  </div>
</template>
```

### Step 3: Test Integration

1. **Connect**: Click "Connect Google Calendar" button
2. **Authorize**: Complete OAuth flow in popup
3. **Sync**: Events from Google Calendar will import automatically
4. **Create**: New events in PIB will sync to Google Calendar

---

## 🧪 Testing Your Setup

### Unit Tests

```bash
# Run calendar composable tests
pnpm test:unit useCalendarEvents

# Run all calendar tests
pnpm test:unit calendar
```

### Integration Tests

```bash
# Start Firebase emulators
pnpm emulators &

# Run integration tests
pnpm test:integration calendar

# Stop emulators
pnpm emulators:stop
```

### End-to-End Tests

```bash
# Run calendar workflow tests
pnpm test:e2e calendar-workflows

# Run Google integration tests
pnpm test:e2e google-integration
```

---

## 🛠️ Common Tasks

### Add a Custom Event Category

```typescript
// In your types file
export type EventCategory
  = | 'customer'
    | 'internal'
    | 'team'
    | 'personal'
    | 'workshop' // <- New category
    | 'none'

// Update category theme
export const categoryTheme = {
  workshop: {
    name: 'Workshop',
    color: 'bg-purple-500',
    outline: 'border-purple-200'
  }
  // ... other categories
}
```

### Customize Calendar View

```vue
<script setup>
const { settings, updateSettings } = useCalendarSettings()

// Set up your preferred view
onMounted(async () => {
  await updateSettings({
    hourOpen: 7, // Start at 7 AM
    hourClose: 20, // End at 8 PM
    hourPrecision: 30, // 30-minute slots
    hideWeekends: true, // Hide weekends
    weekStartsOn: 1 // Start week on Monday
  })
})
</script>
```

### Handle Sync Conflicts

```typescript
// Custom conflict resolution
const { syncCalendar } = useGoogleCalendar()

async function handleSyncWithConflicts() {
  try {
    await syncCalendar('bidirectional')
  }
  catch (error) {
    if (error.type === 'CONFLICT_DETECTED') {
      // Show conflict resolution UI
      await showConflictDialog(error.conflicts)
    }
  }
}
```

---

## 📊 Monitoring & Debugging

### Enable Debug Logging

```typescript
// In your main app file
if (process.dev) {
  // Enable Firestore debug logging
  import { enableNetwork } from 'firebase/firestore'

  console.log('Calendar debug mode enabled')

  // Log calendar operations
  window.debugCalendar = true
}
```

### Check Calendar Health

```vue
<script setup>
async function healthCheck() {
  const { calendarEvents, loading, error } = useCalendarEvents({
    fromDate: ref(new Date()),
    toDate: ref(new Date())
  })

  console.log('Calendar Health Check:')
  console.log('- Events loaded:', !loading.value)
  console.log('- Error state:', error.value)
  console.log('- Event count:', calendarEvents.value.length)

  // Test Google integration
  const { isConnected } = useGoogleCalendar()
  console.log('- Google connected:', isConnected.value)
}
</script>
```

### Performance Monitoring

```typescript
// Monitor calendar performance
const performanceMonitor = {
  startTime: Date.now(),

  logOperation: (operation: string, startTime: number) => {
    const duration = Date.now() - startTime
    console.log(`Calendar ${operation}: ${duration}ms`)

    if (duration > 1000) {
      console.warn(`Slow calendar operation: ${operation}`)
    }
  }
}

// Use in composables
const { createEvent } = useCalendarEvents()

async function monitoredCreateEvent(eventData) {
  const start = Date.now()
  const result = await createEvent(eventData)
  performanceMonitor.logOperation('createEvent', start)
  return result
}
```

---

## 🚨 Troubleshooting

### Common Issues

#### Events Not Persisting

**Problem**: Events disappear after page refresh

**Solution**:
```bash
# Check Firestore rules
firebase firestore:rules:get

# Verify user authentication
console.log('User authenticated:', !!useAuth().currentUser.value)

# Check console for permission errors
```

#### Google OAuth Not Working

**Problem**: OAuth popup closes without connecting

**Solutions**:
1. **Check redirect URI**: Must match Google Cloud Console exactly
2. **Verify HTTPS**: Production requires HTTPS
3. **Check popup blockers**: Ensure popups are allowed

```typescript
// Debug OAuth issues
function debugOAuth() {
  console.log('Client ID:', process.env.GOOGLE_CLIENT_ID)
  console.log('Redirect URI:', `${process.env.NUXT_PUBLIC_BASE_URL}/api/integrations/google-calendar/callback`)
}
```

#### Sync Not Working

**Problem**: Events not syncing with Google Calendar

**Solutions**:
1. **Check network**: Ensure internet connection
2. **Verify tokens**: Check if access token is valid
3. **Review permissions**: Ensure calendar permissions granted

```typescript
// Test sync manually
async function testSync() {
  const { syncCalendar } = useGoogleCalendar()

  try {
    const result = await syncCalendar('import')
    console.log('Sync successful:', result)
  }
  catch (error) {
    console.error('Sync failed:', error)
  }
}
```

### Getting Help

1. **Check Console**: Look for error messages in browser console
2. **Review Logs**: Check Firebase console for backend errors
3. **Test Emulators**: Use Firebase emulators for local testing
4. **Documentation**: Refer to full documentation at `/docs/calendar-system-documentation.md`

---

## 🎯 Next Steps

### Expand Functionality

1. **Add More Providers**: Implement Outlook or Apple Calendar
2. **Recurring Events**: Add support for repeating events
3. **Meeting Rooms**: Add resource booking functionality
4. **Notifications**: Implement email/push notifications

### Optimize Performance

1. **Implement Caching**: Add client-side event caching
2. **Optimize Queries**: Use Firestore composite indexes
3. **Lazy Loading**: Load events on demand

### Enhance Security

1. **Audit Permissions**: Review Firestore security rules
2. **Token Rotation**: Implement automatic token refresh
3. **Rate Limiting**: Add API rate limiting

### Production Deployment

1. **Environment Setup**: Configure production environment variables
2. **SSL Certificates**: Ensure HTTPS for OAuth
3. **Monitoring**: Set up error tracking and performance monitoring
4. **Backup Strategy**: Implement data backup procedures

---

## 📚 Additional Resources

- **Full Documentation**: `/docs/calendar-system-documentation.md`
- **API Reference**: `/docs/calendar-api-reference.md`
- **Testing Guide**: `/docs/calendar-testing-strategy.md`
- **Google Calendar API**: [https://developers.google.com/calendar](https://developers.google.com/calendar)
- **Firebase Documentation**: [https://firebase.google.com/docs](https://firebase.google.com/docs)

---

**Congratulations!** 🎉 You now have a fully functional calendar system with real-time data persistence and Google Calendar integration. Start creating events and explore the advanced features to enhance your productivity workflow.
