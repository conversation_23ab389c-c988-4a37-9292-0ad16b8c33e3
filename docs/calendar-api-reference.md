# Calendar API Reference

## 📚 Overview

This document provides comprehensive API reference for the Calendar Integration System, including composables, server endpoints, and data structures.

---

## 🔧 Composables API

### `useCalendarEvents(props: UseCalendarEventsProps)`

Main composable for calendar event management with real-time Firestore integration.

#### Parameters

```typescript
interface UseCalendarEventsProps {
  fromDate: MaybeRefOrGetter<Date>
  toDate: MaybeRefOrGetter<Date>
}
```

#### Returns

```typescript
interface UseCalendarEventsReturn {
  // State
  calendarEvents: Readonly<Ref<CalendarCustomAttribute<CalendarEvent>[]>>
  loading: Readonly<Ref<boolean>>
  error: Readonly<Ref<string | null>>

  // Methods
  createEvent: (eventData: Partial<CalendarEvent>) => Promise<CalendarEvent>
  updateEvent: (eventId: string, updates: Partial<CalendarEvent>) => Promise<void>
  deleteEvent: (eventId: string) => Promise<void>
  subscribeToEvents: () => Promise<void>
  unsubscribe: () => void
}
```

#### Usage Example

```typescript
<script setup>
const { fromDate, toDate } = useDateRange(settings)
const {
  calendarEvents,
  loading,
  error,
  createEvent,
  updateEvent,
  deleteEvent
} = useCalendarEvents({ fromDate, toDate })

// Create a new event
const newEvent = await createEvent({
  title: 'Team Meeting',
  startDate: new Date('2024-01-15T10:00:00'),
  endDate: new Date('2024-01-15T11:00:00'),
  category: 'team'
})

// Update an existing event
await updateEvent(eventId, {
  title: 'Updated Meeting Title',
  duration: 90
})

// Delete an event (soft delete)
await deleteEvent(eventId)
</script>
```

#### Error Handling

The composable handles various error scenarios:

- **Authentication errors**: User not logged in or insufficient permissions
- **Validation errors**: Invalid event data or missing required fields
- **Network errors**: Firestore connection issues or rate limiting
- **Sync errors**: Conflicts with external calendar providers

---

### `useCalendarSettings()`

Manages user calendar preferences and settings with automatic persistence.

#### Returns

```typescript
interface UseCalendarSettingsReturn {
  // State
  settings: Readonly<Ref<CalendarSettings | null>>
  loading: Readonly<Ref<boolean>>

  // Methods
  loadSettings: () => Promise<void>
  updateSettings: (updates: Partial<CalendarSettings>) => Promise<void>
}
```

#### Usage Example

```typescript
<script setup>
const { settings, loading, updateSettings } = useCalendarSettings()

// Update calendar view preferences
await updateSettings({
  hideWeekends: true,
  hourOpen: 9,
  hourClose: 17,
  weekStartsOn: 1 // Monday
})

// Update sync preferences
await updateSettings({
  autoSync: true,
  syncFrequency: 30 // minutes
})
</script>
```

#### Settings Schema

```typescript
interface CalendarSettings {
  // UI Settings
  hideWeekends: boolean
  hourOpen: number // 0-23
  hourClose: number // 1-24
  hourPrecision: number // minutes (5, 10, 15, 30)
  hourHeight: number // pixels
  weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6
  defaultView: 'day' | 'week' | 'month'
  timezone: string

  // Sync Settings
  autoSync: boolean
  syncFrequency: number // minutes
  syncConflictResolution: 'local_wins' | 'remote_wins' | 'merge' | 'ask_user'

  // Notification Settings
  defaultReminders: {
    type: 'email' | 'push' | 'popup'
    minutesBefore: number
  }[]
}
```

---

### `useGoogleCalendar()`

Handles Google Calendar integration, OAuth flow, and synchronization.

#### Returns

```typescript
interface UseGoogleCalendarReturn {
  // State
  isConnected: Readonly<Ref<boolean>>
  isConnecting: Readonly<Ref<boolean>>
  lastSyncTime: Readonly<Ref<Date | null>>
  syncStatus: Readonly<Ref<'idle' | 'syncing' | 'error'>>

  // Methods
  connectToGoogle: () => Promise<void>
  disconnect: () => Promise<void>
  syncCalendar: (direction?: SyncDirection) => Promise<SyncResult>
  getCalendarList: () => Promise<GoogleCalendar[]>
}
```

#### Usage Example

```typescript
<script setup>
const {
  isConnected,
  isConnecting,
  connectToGoogle,
  disconnect,
  syncCalendar
} = useGoogleCalendar()

// Connect to Google Calendar
const handleConnect = async () => {
  try {
    await connectToGoogle()
    console.log('Successfully connected to Google Calendar')
  } catch (error) {
    console.error('Failed to connect:', error)
  }
}

// Sync calendar events
const handleSync = async () => {
  try {
    const result = await syncCalendar('bidirectional')
    console.log(`Synced ${result.eventsProcessed} events`)
  } catch (error) {
    console.error('Sync failed:', error)
  }
}

// Disconnect from Google Calendar
const handleDisconnect = async () => {
  await disconnect()
  console.log('Disconnected from Google Calendar')
}
</script>
```

#### Sync Direction Types

```typescript
type SyncDirection = 'import' | 'export' | 'bidirectional'

interface SyncResult {
  success: boolean
  eventsProcessed: number
  eventsCreated: number
  eventsUpdated: number
  eventsDeleted: number
  errors: string[]
  duration: number // milliseconds
}
```

---

## 🌐 Server API Endpoints

### Google Calendar Integration

#### `POST /api/integrations/google-calendar/oauth/init`

Initiates Google Calendar OAuth 2.0 flow with PKCE.

**Request Body:**
```typescript
{
  workspaceId: string
  profileId?: string
}
```

**Response:**
```typescript
{
  authUrl: string // Google OAuth authorization URL
  state: string // OAuth state parameter for CSRF protection
}
```

**Example:**
```typescript
const response = await $fetch('/api/integrations/google-calendar/oauth/init', {
  method: 'POST',
  body: {
    workspaceId: 'workspace_123',
    profileId: 'profile_456'
  }
})

// Redirect user to OAuth URL
window.location.href = response.authUrl
```

---

#### `GET /api/integrations/google-calendar/callback`

Handles OAuth callback and exchanges authorization code for access tokens.

**Query Parameters:**
- `code`: Authorization code from Google OAuth flow
- `state`: OAuth state parameter for validation

**Response:**
```typescript
{
  success: boolean
  integration?: {
    id: string
    provider: 'google-calendar'
    isActive: boolean
    connectedAt: string
  }
  error?: string
}
```

**Example:**
```typescript
// This endpoint is called automatically by Google OAuth flow
// URL: /api/integrations/google-calendar/callback?code=AUTH_CODE&state=STATE
```

---

#### `POST /api/integrations/google-calendar/sync`

Triggers manual synchronization with Google Calendar.

**Request Body:**
```typescript
{
  integrationId: string
  direction?: 'import' | 'export' | 'bidirectional' // default: 'bidirectional'
}
```

**Response:**
```typescript
{
  success: boolean
  eventsProcessed: number
  eventsCreated: number
  eventsUpdated: number
  eventsDeleted: number
  errors?: string[]
  syncLog: {
    id: string
    startTime: string
    endTime: string
    duration: number
  }
}
```

**Example:**
```typescript
const syncResult = await $fetch('/api/integrations/google-calendar/sync', {
  method: 'POST',
  body: {
    integrationId: 'integration_123',
    direction: 'bidirectional'
  }
})

console.log(`Processed ${syncResult.eventsProcessed} events`)
```

---

#### `POST /api/integrations/google-calendar/disconnect`

Disconnects Google Calendar integration and revokes access tokens.

**Request Body:**
```typescript
{
  integrationId: string
}
```

**Response:**
```typescript
{
  success: boolean
  message: string
}
```

**Example:**
```typescript
await $fetch('/api/integrations/google-calendar/disconnect', {
  method: 'POST',
  body: {
    integrationId: 'integration_123'
  }
})
```

---

#### `GET /api/integrations/calendar`

Retrieves user's calendar integrations and their status.

**Response:**
```typescript
{
  integrations: CalendarIntegration[]
}

interface CalendarIntegration {
  id: string
  provider: 'google-calendar' | 'outlook-calendar' | 'apple-calendar'
  isActive: boolean
  connectedAt: string
  lastSync?: string
  settings: {
    calendars: {
      id: string
      name: string
      color: string
      isEnabled: boolean
    }[]
    syncFrequency: number
  }
}
```

**Example:**
```typescript
const { integrations } = await $fetch('/api/integrations/calendar')

integrations.forEach((integration) => {
  console.log(`${integration.provider}: ${integration.isActive ? 'Connected' : 'Disconnected'}`)
})
```

---

## 📊 Data Types

### Core Calendar Types

#### `CalendarEvent`

```typescript
interface CalendarEvent {
  // Identifiers
  id: string
  userId: string
  workspaceId: string
  profileId?: string

  // Event Details
  title: string
  description?: string
  location?: string
  category: EventCategory

  // Timing
  startDate: Date | Timestamp
  endDate: Date | Timestamp
  duration: number // minutes
  isAllDay: boolean
  timezone: string

  // Recurrence (optional)
  recurrence?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
    interval: number
    endDate?: Date
    count?: number
    byDay?: number[]
    byMonthDay?: number[]
  }

  // Participants
  participants: {
    id: string
    name: string
    email: string
    photo?: string
    status: 'pending' | 'accepted' | 'declined' | 'tentative'
    isRequired: boolean
  }[]

  // Features
  features?: {
    record?: boolean
    drive?: boolean
    external?: boolean
    conversation?: boolean
  }

  // Google Calendar Integration
  googleEventId?: string
  googleCalendarId?: string
  syncStatus: 'synced' | 'pending' | 'error' | 'local_only'
  lastSynced?: Date

  // Status
  status: 'confirmed' | 'tentative' | 'cancelled'
  visibility: 'public' | 'private' | 'confidential'

  // Metadata
  createdAt: Date | Timestamp
  updatedAt: Date | Timestamp
  deletedAt?: Date | Timestamp | null
}
```

#### `EventCategory`

```typescript
type EventCategory
  = | 'customer' // Client meetings, customer calls
    | 'internal' // Company meetings, all-hands
    | 'team' // Team meetings, standups
    | 'personal' // Personal appointments, breaks
    | 'none' // Uncategorized events
```

#### `CalendarCustomAttribute`

```typescript
interface CalendarCustomAttribute<T = CalendarEvent> {
  key: string
  customData: T
  dates: Date[]
  // VCalendar attribute properties
  order?: number
  popover?: object
  highlight?: object | boolean
  content?: object | boolean
  dot?: object | boolean
  bar?: object | boolean
}
```

### Google Calendar Integration Types

#### `GoogleCalendarEvent`

```typescript
interface GoogleCalendarEvent {
  id: string
  summary: string
  description?: string
  location?: string
  start: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  end: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  attendees?: {
    email: string
    displayName?: string
    responseStatus: 'needsAction' | 'declined' | 'tentative' | 'accepted'
  }[]
  status: 'confirmed' | 'tentative' | 'cancelled'
  visibility: 'default' | 'public' | 'private' | 'confidential'
  created: string
  updated: string
}
```

#### `GoogleCalendar`

```typescript
interface GoogleCalendar {
  id: string
  summary: string
  description?: string
  location?: string
  timeZone: string
  colorId?: string
  backgroundColor?: string
  foregroundColor?: string
  selected?: boolean
  accessRole: 'freeBusyReader' | 'reader' | 'writer' | 'owner'
  primary?: boolean
}
```

---

## 🔒 Error Handling

### Error Types

```typescript
enum CalendarErrorType {
  // Authentication Errors
  AUTH_REQUIRED = 'auth_required',
  AUTH_EXPIRED = 'auth_expired',
  PERMISSION_DENIED = 'permission_denied',

  // Validation Errors
  INVALID_EVENT_DATA = 'invalid_event_data',
  MISSING_REQUIRED_FIELD = 'missing_required_field',
  INVALID_DATE_RANGE = 'invalid_date_range',

  // Network Errors
  NETWORK_ERROR = 'network_error',
  RATE_LIMITED = 'rate_limited',
  SERVICE_UNAVAILABLE = 'service_unavailable',

  // Sync Errors
  SYNC_FAILED = 'sync_failed',
  CONFLICT_DETECTED = 'conflict_detected',
  INTEGRATION_NOT_FOUND = 'integration_not_found',

  // General Errors
  UNKNOWN_ERROR = 'unknown_error'
}

interface CalendarError {
  type: CalendarErrorType
  message: string
  details?: any
  timestamp: Date
}
```

### Error Handling Examples

```typescript
// Handle authentication errors
try {
  await createEvent(eventData)
}
catch (error) {
  if (error.type === CalendarErrorType.AUTH_REQUIRED) {
    // Redirect to login
    await navigateTo('/auth/login')
  }
  else if (error.type === CalendarErrorType.PERMISSION_DENIED) {
    // Show permission error
    toast.add({
      title: 'Permission Denied',
      description: 'You do not have permission to create events',
      color: 'red'
    })
  }
}

// Handle sync errors
try {
  await syncCalendar()
}
catch (error) {
  if (error.type === CalendarErrorType.CONFLICT_DETECTED) {
    // Show conflict resolution UI
    showConflictResolution(error.details)
  }
  else if (error.type === CalendarErrorType.RATE_LIMITED) {
    // Schedule retry
    setTimeout(() => syncCalendar(), error.details.retryAfter * 1000)
  }
}
```

---

## 🎯 Best Practices

### Performance Optimization

#### 1. Efficient Firestore Queries

```typescript
// Good: Use specific date ranges
const q = query(
  collection(db, 'calendar_events'),
  where('workspaceId', '==', workspaceId),
  where('startDate', '>=', startOfWeek),
  where('startDate', '<=', endOfWeek),
  orderBy('startDate'),
  limit(100)
)

// Avoid: Querying all events
const badQuery = query(collection(db, 'calendar_events'))
```

#### 2. Subscription Management

```typescript
// Good: Clean up subscriptions
const unsubscribe = ref<Unsubscribe | null>(null)

onMounted(() => {
  unsubscribe.value = onSnapshot(query, callback)
})

onUnmounted(() => {
  unsubscribe.value?.()
})

// Avoid: Memory leaks from unmanaged subscriptions
```

#### 3. Debounced Updates

```typescript
// Good: Debounce frequent updates
const debouncedUpdateSettings = debounce(updateSettings, 500)

watch(settings, (newSettings) => {
  debouncedUpdateSettings(newSettings)
})

// Avoid: Excessive API calls on every change
```

### Security Considerations

#### 1. Input Validation

```typescript
// Validate event data before saving
function validateEventData(data: Partial<CalendarEvent>) {
  if (!data.title?.trim()) {
    throw new Error('Event title is required')
  }

  if (!data.startDate || !data.endDate) {
    throw new Error('Start and end dates are required')
  }

  if (new Date(data.startDate) >= new Date(data.endDate)) {
    throw new Error('End date must be after start date')
  }
}
```

#### 2. Workspace Isolation

```typescript
// Ensure events are workspace-scoped
async function createEvent(eventData: Partial<CalendarEvent>) {
  const { currentWorkspace } = useAuth()

  if (!currentWorkspace.value) {
    throw new Error('No active workspace')
  }

  return await addDoc(collection(db, 'calendar_events'), {
    ...eventData,
    workspaceId: currentWorkspace.value.id,
    userId: currentUser.value.id
  })
}
```

#### 3. Sensitive Data Handling

```typescript
// Encrypt sensitive integration data
async function storeIntegration(tokens: GoogleTokens) {
  const encrypted = await encrypt(tokens)

  await setDoc(doc(db, 'integrations', integrationId), {
    provider: 'google-calendar',
    credentials: encrypted,
    encryptedAt: serverTimestamp()
  })
}
```

### Testing Patterns

#### 1. Mock Firestore Operations

```typescript
// Mock Firestore for unit tests
const mockFirestore = {
  collection: vi.fn(),
  doc: vi.fn(),
  addDoc: vi.fn(),
  updateDoc: vi.fn(),
  onSnapshot: vi.fn()
}

vi.mock('firebase/firestore', () => mockFirestore)
```

#### 2. Test Real-time Subscriptions

```typescript
test('should handle real-time updates', async () => {
  const { calendarEvents } = useCalendarEvents({
    fromDate: ref(new Date()),
    toDate: ref(new Date())
  })

  // Simulate Firestore update
  const mockSnapshot = {
    docs: [{ id: '1', data: () => mockEventData }]
  }

  // Trigger snapshot callback
  mockFirestore.onSnapshot.mock.calls[0][1](mockSnapshot)

  await nextTick()
  expect(calendarEvents.value).toHaveLength(1)
})
```

---

This API reference provides comprehensive documentation for all calendar system components, enabling developers to effectively integrate and extend the calendar functionality.
