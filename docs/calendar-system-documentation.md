# Calendar Integration System Documentation

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [API Documentation](#api-documentation)
4. [Components & Composables](#components--composables)
5. [Database Schema](#database-schema)
6. [Integration Guides](#integration-guides)
7. [Testing Strategy](#testing-strategy)
8. [Deployment Guide](#deployment-guide)
9. [Troubleshooting](#troubleshooting)
10. [LEVER Framework Compliance](#lever-framework-compliance)

---

## System Overview

### Purpose
The Calendar Integration System transforms the PIB application's calendar from a demo with mock data into a fully-functional, production-ready calendar system with real-time data persistence and third-party calendar integration.

### Key Features
- **Real-time Data Persistence**: Events stored in Firebase Firestore with live updates
- **Google Calendar Integration**: Bidirectional sync with OAuth 2.0 authentication
- **Drag & Drop Functionality**: Intuitive event management with persistence
- **Multi-workspace Support**: Calendar events isolated by workspace
- **Offline Capability**: Local state management with sync when online
- **Conflict Resolution**: Smart handling of concurrent edits and sync conflicts

### Technology Stack
- **Frontend**: Nuxt 3, Vue 3 Composition API, TypeScript
- **Backend**: Firebase (Firestore, Auth, Functions)
- **UI**: Tailwind CSS, Shuriken UI components
- **Calendar Library**: v-calendar with custom extensions
- **Testing**: Vitest, Playwright, Vue Test Utils

---

## Architecture

### System Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        A[Calendar UI] --> B[Calendar Composables]
        B --> C[Firebase SDK]
        A --> D[Integration UI]
        D --> E[OAuth Flow]
    end

    subgraph "Server Layer"
        F[Nuxt Server API] --> G[Google Calendar API]
        F --> H[Firebase Admin]
    end

    subgraph "Data Layer"
        I[Firestore Collections]
        J[Firebase Auth]
        K[Google Calendar]
    end

    C --> I
    C --> J
    E --> F
    H --> I
    G --> K

    classDef client fill:#e1f5fe
    classDef server fill:#f3e5f5
    classDef data fill:#e8f5e8

    class A,B,C,D,E client
    class F,G,H server
    class I,J,K data
```

### Data Flow

#### Real-time Event Management
1. **User Action**: Create/edit/delete event in UI
2. **Validation**: Client-side validation and optimistic updates
3. **Firestore Write**: Event saved to Firestore with security rules
4. **Real-time Sync**: All connected clients receive updates via onSnapshot
5. **External Sync**: Background process syncs with Google Calendar

#### Calendar Integration Flow
1. **OAuth Initiation**: User clicks "Connect Google Calendar"
2. **Server Redirect**: Nuxt API generates OAuth URL with PKCE
3. **User Consent**: Google OAuth consent screen
4. **Token Exchange**: Server exchanges auth code for tokens
5. **Store Credentials**: Encrypted tokens saved in Firestore
6. **Initial Sync**: Import existing Google Calendar events
7. **Ongoing Sync**: Bidirectional sync with conflict resolution

---

## API Documentation

### Firestore Collections

#### `calendar_events`
```typescript
interface CalendarEvent {
  id: string
  userId: string
  workspaceId: string
  profileId?: string

  // Event Details
  title: string
  description?: string
  location?: string
  category: 'customer' | 'internal' | 'team' | 'personal' | 'none'

  // Timing
  startDate: Timestamp
  endDate: Timestamp
  duration: number // minutes
  isAllDay: boolean
  timezone: string

  // Google Calendar Integration
  googleEventId?: string
  googleCalendarId?: string
  syncStatus: 'synced' | 'pending' | 'error' | 'local_only'
  lastSynced?: Timestamp

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  deletedAt?: Timestamp | null
}
```

#### `calendar_settings`
```typescript
interface CalendarSettings {
  id: string // userId
  userId: string
  workspaceId: string

  // UI Settings
  hideWeekends: boolean
  hourOpen: number
  hourClose: number
  hourPrecision: number
  hourHeight: number
  weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6

  // Sync Settings
  autoSync: boolean
  syncFrequency: number // minutes

  createdAt: Timestamp
  updatedAt: Timestamp
}
```

### Server API Endpoints

#### Google Calendar Integration

##### `POST /api/integrations/google-calendar/oauth/init`
Initiates Google Calendar OAuth flow.

**Request:**
```typescript
{
  workspaceId: string
  profileId?: string
}
```

**Response:**
```typescript
{
  authUrl: string // Google OAuth URL with PKCE
  state: string // OAuth state parameter
}
```

##### `GET /api/integrations/google-calendar/callback`
Handles OAuth callback and exchanges auth code for tokens.

**Query Parameters:**
- `code`: Authorization code from Google
- `state`: OAuth state parameter

**Response:**
```typescript
{
  success: boolean
  integration?: {
    id: string
    provider: 'google-calendar'
    isActive: boolean
  }
  error?: string
}
```

##### `POST /api/integrations/google-calendar/sync`
Triggers manual sync with Google Calendar.

**Request:**
```typescript
{
  integrationId: string
  direction: 'import' | 'export' | 'bidirectional'
}
```

**Response:**
```typescript
{
  success: boolean
  eventsProcessed: number
  eventsCreated: number
  eventsUpdated: number
  errors?: string[]
}
```

---

## Components & Composables

### Core Composables

#### `useCalendarEvents()`
Main composable for calendar event management with real-time Firestore integration.

**Usage:**
```typescript
const { fromDate, toDate } = useDateRange(settings)
const {
  calendarEvents,
  loading,
  error,
  createEvent,
  updateEvent,
  deleteEvent
} = useCalendarEvents({ fromDate, toDate })
```

**Methods:**
- `createEvent(eventData)`: Creates new calendar event
- `updateEvent(eventId, updates)`: Updates existing event
- `deleteEvent(eventId)`: Soft deletes event
- `subscribeToEvents()`: Establishes real-time subscription

**State:**
- `calendarEvents`: Reactive array of calendar events
- `loading`: Boolean indicating loading state
- `error`: Error message if operations fail

#### `useCalendarSettings()`
Manages user calendar preferences and settings.

**Usage:**
```typescript
const {
  settings,
  loading,
  updateSettings
} = useCalendarSettings()
```

**Methods:**
- `updateSettings(updates)`: Updates user calendar settings
- `loadSettings()`: Loads settings from Firestore

#### `useGoogleCalendar()`
Handles Google Calendar integration and OAuth flow.

**Usage:**
```typescript
const {
  isConnected,
  isConnecting,
  connectToGoogle,
  disconnect,
  syncCalendar
} = useGoogleCalendar()
```

**Methods:**
- `connectToGoogle()`: Initiates OAuth flow
- `disconnect()`: Removes Google Calendar integration
- `syncCalendar(direction)`: Syncs events with Google Calendar

### UI Components

#### Calendar Page (`/layers/auth-module/pages/calendar.vue`)
Main calendar interface with drag-and-drop functionality.

**Features:**
- Weekly calendar view with customizable time range
- Real-time event updates
- Drag-and-drop event creation and editing
- Event details panel with participant management
- Settings panel for calendar configuration

**Props:** None (uses composables for state management)

**Events:**
- Event creation through double-click or drag
- Event editing through selection and form
- Settings changes through settings panel

---

## Database Schema

### Firestore Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Calendar events
    match /calendar_events/{eventId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isWorkspaceMember(resource.data.workspaceId)
      );

      allow create: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid &&
        validateCalendarEvent(request.resource.data);

      allow update: if isAuthenticated() &&
        resource.data.userId == request.auth.uid &&
        validateCalendarEvent(request.resource.data);

      allow delete: if false; // Soft delete only
    }

    // Calendar settings
    match /calendar_settings/{userId} {
      allow read, write: if isAuthenticated() &&
        userId == request.auth.uid;
    }

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isWorkspaceMember(workspaceId) {
      return exists(/databases/$(database)/documents/workspace_members/$(workspaceId + '_' + request.auth.uid));
    }

    function validateCalendarEvent(data) {
      return data.keys().hasAll(['title', 'startDate', 'endDate', 'userId']) &&
             data.title is string &&
             data.startDate is timestamp &&
             data.endDate is timestamp;
    }
  }
}
```

### Indexes

Required Firestore indexes for optimal performance:

```javascript
// Composite indexes for calendar_events collection
{
  collection: "calendar_events",
  fields: [
    { fieldPath: "workspaceId", order: "ASCENDING" },
    { fieldPath: "deletedAt", order: "ASCENDING" },
    { fieldPath: "startDate", order: "ASCENDING" }
  ]
}

{
  collection: "calendar_events",
  fields: [
    { fieldPath: "userId", order: "ASCENDING" },
    { fieldPath: "deletedAt", order: "ASCENDING" },
    { fieldPath: "startDate", order: "ASCENDING" }
  ]
}
```

---

## Integration Guides

### Google Calendar Integration Setup

#### 1. Google Cloud Console Configuration

1. **Create Project**: Go to [Google Cloud Console](https://console.cloud.google.com)
2. **Enable APIs**: Enable Google Calendar API
3. **Create Credentials**: Create OAuth 2.0 client ID
4. **Configure Consent Screen**: Set up OAuth consent screen
5. **Add Redirect URIs**: Add your app's callback URLs

#### 2. Environment Variables

```bash
# .env
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
NUXT_PUBLIC_BASE_URL=https://your-app.com
```

#### 3. OAuth Flow Implementation

```typescript
// Client-side OAuth initiation
const { connectToGoogle } = useGoogleCalendar()

await connectToGoogle() // Opens OAuth popup
```

```typescript
// Server-side OAuth handling
export default defineEventHandler(async (event) => {
  const { code, state } = getQuery(event)

  // Exchange code for tokens
  const tokens = await exchangeCodeForTokens(code)

  // Store in Firestore
  await saveIntegration(userId, tokens)

  return { success: true }
})
```

### Adding New Calendar Providers

#### 1. Extend Integration Types

```typescript
// types/calendar.ts
export type CalendarProvider
  = | 'google-calendar'
    | 'outlook-calendar'
    | 'apple-calendar'

export interface CalendarIntegration extends Integration {
  provider: CalendarProvider
  credentials: {
    accessToken?: string
    refreshToken?: string
    // Provider-specific fields
  }
}
```

#### 2. Create Provider Composable

```typescript
// composables/useOutlookCalendar.ts
export function useOutlookCalendar() {
  const connectToOutlook = async () => {
    // Implement Outlook OAuth flow
  }

  const syncCalendar = async () => {
    // Implement Outlook sync logic
  }

  return {
    connectToOutlook,
    syncCalendar
  }
}
```

#### 3. Add Server API Routes

```typescript
// server/api/integrations/outlook/oauth/init.post.ts
export default defineEventHandler(async (event) => {
  // Implement Outlook OAuth initiation
})
```

---

## Testing Strategy

### Test Coverage Requirements

- **Unit Tests**: 90% coverage for composables and utilities
- **Integration Tests**: Firebase operations and API integrations
- **E2E Tests**: Complete user workflows
- **Performance Tests**: Load testing with large datasets

### Test Structure

```
tests/
├── unit/
│   ├── composables/
│   │   ├── useCalendarEvents.test.ts
│   │   ├── useCalendarSettings.test.ts
│   │   └── useGoogleCalendar.test.ts
│   └── utils/
│       └── calendar-helpers.test.ts
├── integration/
│   ├── firebase/
│   │   ├── calendar-events.test.ts
│   │   └── calendar-settings.test.ts
│   └── api/
│       └── google-calendar.test.ts
├── e2e/
│   ├── calendar-workflows.spec.ts
│   ├── google-integration.spec.ts
│   └── drag-drop.spec.ts
└── performance/
    └── calendar-load.test.ts
```

### Running Tests

```bash
# Unit tests
pnpm test:unit

# Integration tests (requires Firebase emulators)
pnpm emulators &
pnpm test:integration

# E2E tests
pnpm test:e2e

# All tests with coverage
pnpm test:coverage
```

### Test Examples

#### Unit Test - useCalendarEvents

```typescript
import { describe, expect, it, vi } from 'vitest'
import { useCalendarEvents } from '~/composables/useCalendarEvents'

describe('useCalendarEvents', () => {
  it('should create calendar event', async () => {
    const { createEvent } = useCalendarEvents({
      fromDate: ref(new Date()),
      toDate: ref(new Date())
    })

    const eventData = {
      title: 'Test Event',
      startDate: new Date(),
      endDate: new Date(),
      category: 'personal'
    }

    const event = await createEvent(eventData)

    expect(event.id).toBeDefined()
    expect(event.title).toBe('Test Event')
  })
})
```

#### E2E Test - Calendar Workflow

```typescript
import { expect, test } from '@playwright/test'

test('should create and edit calendar event', async ({ page }) => {
  await page.goto('/calendar')

  // Create event by double-clicking
  await page.dblclick('[data-day="2024-01-15"]')

  // Fill event details
  await page.fill('[data-testid="event-title"]', 'Team Meeting')
  await page.selectOption('[data-testid="event-category"]', 'team')

  // Save event
  await page.click('[data-testid="save-event"]')

  // Verify event appears in calendar
  await expect(page.locator('text=Team Meeting')).toBeVisible()
})
```

---

## Deployment Guide

### Prerequisites

1. **Node.js**: Version 20+
2. **pnpm**: Package manager
3. **Firebase CLI**: For deployment
4. **Google Cloud Account**: For Calendar API

### Environment Setup

#### 1. Install Dependencies

```bash
pnpm install
```

#### 2. Firebase Configuration

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize project (if not already done)
firebase init
```

#### 3. Environment Variables

Create `.env` file with required variables:

```bash
# Firebase Configuration
NUXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NUXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NUXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NUXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com

# Google Calendar API
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Application
NUXT_PUBLIC_BASE_URL=https://your-app.com
```

### Deployment Steps

#### 1. Build Application

```bash
pnpm build
```

#### 2. Deploy Firestore Rules

```bash
pnpm deploy:rules
```

#### 3. Deploy to Hosting Platform

**Vercel:**
```bash
vercel --prod
```

**Netlify:**
```bash
netlify deploy --prod
```

**Firebase Hosting:**
```bash
firebase deploy --only hosting
```

#### 4. Verify Deployment

1. **Health Check**: Visit `/api/health` endpoint
2. **Calendar Access**: Test calendar page loads
3. **Google Integration**: Test OAuth flow
4. **Database Operations**: Create/edit/delete events

### Production Checklist

- [ ] Environment variables configured
- [ ] Firestore security rules deployed
- [ ] Google OAuth credentials configured
- [ ] SSL/TLS certificate installed
- [ ] Performance monitoring enabled
- [ ] Error tracking configured
- [ ] Backup strategy implemented

---

## Troubleshooting

### Common Issues

#### 1. OAuth Flow Failures

**Symptom**: Google OAuth popup closes without connecting

**Causes & Solutions:**
- **Invalid redirect URI**: Verify redirect URI in Google Cloud Console matches exactly
- **Popup blocked**: Ensure popup blockers are disabled
- **HTTPS required**: Google OAuth requires HTTPS in production

```typescript
// Debug OAuth issues
console.log('OAuth URL:', authUrl)
console.log('Redirect URI:', redirectUri)
console.log('Client ID:', clientId)
```

#### 2. Firestore Permission Errors

**Symptom**: "Permission denied" when accessing calendar events

**Causes & Solutions:**
- **Security rules**: Verify Firestore rules allow access
- **Authentication**: Ensure user is properly authenticated
- **Workspace membership**: Check user belongs to workspace

```javascript
// Debug security rules
match /calendar_events/{eventId} {
  allow read: if debug(resource.data.userId == request.auth.uid);
}
```

#### 3. Real-time Updates Not Working

**Symptom**: Calendar events don't update automatically

**Causes & Solutions:**
- **Subscription cleanup**: Ensure onSnapshot listeners are properly managed
- **Network issues**: Check network connectivity
- **Firestore offline**: Verify Firestore online status

```typescript
// Debug real-time subscriptions
const unsubscribe = onSnapshot(query, (snapshot) => {
  console.log('Received update:', snapshot.size)
}, (error) => {
  console.error('Subscription error:', error)
})
```

#### 4. Sync Conflicts

**Symptom**: Events duplicated or lost during sync

**Causes & Solutions:**
- **Concurrent edits**: Implement proper conflict resolution
- **Network interruptions**: Add retry mechanisms
- **Timestamp issues**: Ensure consistent timezone handling

```typescript
// Debug sync conflicts
function resolveConflict(local: CalendarEvent, remote: GoogleEvent) {
  console.log('Conflict detected:', {
    local: local.updatedAt,
    remote: new Date(remote.updated)
  })

  // Implement resolution logic
}
```

### Performance Issues

#### 1. Slow Calendar Loading

**Optimization Strategies:**
- **Query optimization**: Use proper Firestore indexes
- **Data pagination**: Load events in chunks
- **Caching**: Implement client-side caching

```typescript
// Optimize Firestore queries
const q = query(
  collection(db, 'calendar_events'),
  where('workspaceId', '==', workspaceId),
  where('startDate', '>=', startDate),
  where('startDate', '<=', endDate),
  orderBy('startDate'),
  limit(50) // Pagination
)
```

#### 2. Memory Leaks

**Prevention Strategies:**
- **Cleanup subscriptions**: Unsubscribe on component unmount
- **Debounce updates**: Prevent excessive API calls
- **Weak references**: Use weak maps for temporary data

```typescript
// Proper subscription cleanup
onUnmounted(() => {
  unsubscribe?.()
})
```

### Debugging Tools

#### 1. Firebase Debug Mode

```typescript
// Enable Firestore debug logging
import { disableNetwork, enableNetwork } from 'firebase/firestore'

// Check network status
console.log('Firestore online:', await enableNetwork(db))
```

#### 2. Calendar Event Debugging

```typescript
// Debug calendar event data
function debugEvent(event: CalendarEvent) {
  console.log('Event Debug:', {
    id: event.id,
    title: event.title,
    startDate: event.startDate.toDate(),
    endDate: event.endDate.toDate(),
    syncStatus: event.syncStatus
  })
}
```

#### 3. Google API Debugging

```typescript
// Debug Google Calendar API calls
function debugGoogleAPI(operation: string, data: any) {
  console.log(`Google API ${operation}:`, {
    timestamp: new Date().toISOString(),
    data: JSON.stringify(data, null, 2)
  })
}
```

---

## LEVER Framework Compliance

### **Leverage** ✅

**Existing Infrastructure Utilized:**
- Firebase/Firestore database and authentication system
- Nuxt 3 framework with Vue 3 Composition API
- Existing UI components and styling (Tairo, Tailwind CSS)
- Current integration management system in auth-module
- Established workspace/profile management architecture

**Code Reuse:**
- Extended existing `CalendarEvent` types instead of creating new ones
- Built upon current authentication patterns and user management
- Utilized existing Firebase security rules patterns
- Leveraged current error handling and notification systems

### **Extend** ✅

**Enhanced Existing Components:**
- `useCalendarEvents` composable enhanced with real Firestore operations
- Calendar UI component extended with real-time data capabilities
- Integration system extended to support calendar providers
- Existing types extended with Firestore and Google Calendar fields

**Incremental Improvements:**
- Added real-time subscriptions while maintaining existing UI behavior
- Enhanced drag-and-drop functionality with persistence
- Extended settings management with calendar preferences
- Improved error handling while maintaining existing patterns

### **Verify** ✅

**Reactive Data Validation:**
- Real-time Firestore subscriptions maintain Vue 3 reactivity
- Calendar events update automatically across all connected clients
- Settings changes reflect immediately in the UI
- Integration status updates in real-time

**Consistency Checks:**
- TypeScript strict mode ensures type safety throughout
- Comprehensive test suite validates all functionality
- Error boundaries prevent system failures
- Performance monitoring ensures scalability

### **Eliminate** ✅

**Removed Redundancies:**
- Replaced mock data generators with real Firestore operations
- Eliminated duplicate authentication patterns
- Removed hardcoded event data and participants
- Consolidated calendar state management

**Cleaned Up Technical Debt:**
- Removed unused mock data functions
- Eliminated temporary state that's now persisted
- Cleaned up development-only code paths
- Removed duplicate type definitions

### **Reduce** ✅

**Simplified Architecture:**
- Maintained existing composable-based patterns
- Used Firebase direct client SDK instead of custom API layer
- Kept minimal server-side components (only for OAuth)
- Reduced complexity while adding comprehensive functionality

**Streamlined Implementation:**
- Single source of truth for calendar data (Firestore)
- Unified error handling across all calendar operations
- Simplified integration management through existing patterns
- Reduced cognitive load with consistent API patterns

---

## Summary

The Calendar Integration System successfully transforms the PIB application's calendar from a demo interface into a production-ready system with:

- **Real-time data persistence** using Firebase Firestore
- **Google Calendar integration** with OAuth 2.0 and bidirectional sync
- **Comprehensive error handling** and user feedback
- **Scalable architecture** following LEVER principles
- **Production-ready testing** with 90% coverage requirements
- **Security-first approach** with proper access controls

The implementation maintains the existing UI design and user experience while adding robust backend functionality, making it ready for production deployment with comprehensive documentation and testing strategies.
