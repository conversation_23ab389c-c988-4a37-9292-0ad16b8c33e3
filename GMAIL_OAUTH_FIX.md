# Gmail OAuth 401 Error Fix

## 🚨 Problem
Gmail OAuth integration was failing with a 401 error:
```
Failed to connect Gmail: Failed to fetch user profile: { 
  "error": { 
    "code": 401, 
    "message": "Request is missing required authentication credential. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.", 
    "status": "UNAUTHENTICATED" 
  } 
}
```

## 🔍 Root Cause
The Gmail OAuth configuration was missing the necessary scopes to access user profile information. The system was trying to fetch user profile data from `https://www.googleapis.com/oauth2/v2/userinfo` but the OAuth token only had Gmail-specific permissions, not the basic profile access permissions.

## ✅ Solution
Added the missing Google OAuth scopes for user profile access:

### Before (Missing Scopes):
```typescript
scopes: [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.modify',
]
```

### After (Complete Scopes):
```typescript
scopes: [
  'https://www.googleapis.com/auth/userinfo.profile',  // ✅ Added
  'https://www.googleapis.com/auth/userinfo.email',    // ✅ Added
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.modify',
]
```

## 📁 Files Modified

1. **`layers/auth-module/config/social-providers.ts`**
   - Added `userinfo.profile` and `userinfo.email` scopes to Gmail configuration

2. **`layers/auth-module/config/email-providers.ts`**
   - Added `userinfo.profile` and `userinfo.email` scopes to Gmail configuration

3. **`OAUTH_SETUP_GUIDE.md`**
   - Updated required scopes documentation

4. **`INTEGRATION_IMPLEMENTATION_SUMMARY.md`**
   - Updated Google OAuth setup instructions with complete scope list

## 🔧 Google Cloud Console Update Required

**IMPORTANT**: You need to update your Google Cloud Console OAuth consent screen to include the new scopes:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **APIs & Services > OAuth consent screen**
3. Click **Edit App**
4. In the **Scopes** section, add:
   - `https://www.googleapis.com/auth/userinfo.profile`
   - `https://www.googleapis.com/auth/userinfo.email`
5. Save the changes

## 🧪 Testing
After updating the scopes in Google Cloud Console:
1. Clear any existing Gmail OAuth cookies/sessions
2. Try connecting Gmail integration again
3. The OAuth flow should now complete successfully
4. User profile information should be fetched without errors

## 📋 Scope Permissions Explained

- **`userinfo.profile`**: Access to basic profile information (name, picture)
- **`userinfo.email`**: Access to user's email address
- **`gmail.readonly`**: Read access to Gmail messages and settings
- **`gmail.send`**: Permission to send emails on user's behalf
- **`gmail.modify`**: Permission to modify emails and labels

## 🔒 Security Note
These additional scopes only provide access to basic profile information that's already available through Google's standard OAuth flow. They don't grant any additional access to sensitive data beyond what's necessary for the integration to function properly.
